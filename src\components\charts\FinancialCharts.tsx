import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  <PERSON><PERSON>xis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON>ltip,
  Legend,
  <PERSON>Chart,
  Pie,
  Cell,
  LineChart,
  Line,
  AreaChart,
  Area,
  ResponsiveContainer
} from 'recharts';

interface FinancialChartsProps {
  data: any;
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];

export const RevenueExpenseChart: React.FC<{ data: any }> = ({ data }) => {
  const chartData = [
    {
      name: 'Current Period',
      Revenue: data?.revenue || 0,
      Expenses: data?.expenses || 0,
      Profit: data?.profit || 0,
    }
  ];

  const isEmpty = !data || (data.revenue === 0 && data.expenses === 0);

  return (
    <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
      <h3 className="text-lg font-semibold text-gray-800 mb-4">Revenue vs Expenses</h3>
      {isEmpty && (
        <div className="text-center py-8 text-gray-500">
          <p>No financial data available</p>
          <p className="text-sm">Load sample data to see revenue and expense trends</p>
        </div>
      )}
      <ResponsiveContainer width="100%" height={300}>
        <BarChart data={chartData}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="name" />
          <YAxis />
          <Tooltip formatter={(value) => `$${Number(value).toLocaleString()}`} />
          <Legend />
          <Bar dataKey="Revenue" fill="#10B981" />
          <Bar dataKey="Expenses" fill="#EF4444" />
          <Bar dataKey="Profit" fill="#3B82F6" />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

export const AssetLiabilityChart: React.FC<{ data: any }> = ({ data }) => {
  const chartData = [
    { name: 'Assets', value: data?.totalAssets || 0, color: '#10B981' },
    { name: 'Liabilities', value: data?.totalLiabilities || 0, color: '#EF4444' },
    { name: 'Net Worth', value: data?.netWorth || 0, color: '#3B82F6' }
  ];

  const isEmpty = !data || (data.totalAssets === 0 && data.totalLiabilities === 0);

  return (
    <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
      <h3 className="text-lg font-semibold text-gray-800 mb-4">Financial Position</h3>
      {isEmpty && (
        <div className="text-center py-8 text-gray-500">
          <p>No balance sheet data available</p>
          <p className="text-sm">Load sample data to see asset and liability breakdown</p>
        </div>
      )}
      <ResponsiveContainer width="100%" height={300}>
        <PieChart>
          <Pie
            data={chartData}
            cx="50%"
            cy="50%"
            labelLine={false}
            label={({ name, value }) => `${name}: $${Number(value).toLocaleString()}`}
            outerRadius={80}
            fill="#8884d8"
            dataKey="value"
          >
            {chartData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color} />
            ))}
          </Pie>
          <Tooltip formatter={(value) => `$${Number(value).toLocaleString()}`} />
        </PieChart>
      </ResponsiveContainer>
    </div>
  );
};

export const InvoiceStatusChart: React.FC<{ data: any }> = ({ data }) => {
  const chartData = [
    { name: 'Paid', value: data?.paidInvoices || 0, amount: data?.paidInvoiceAmount || 0 },
    { name: 'Pending', value: data?.pendingInvoices || 0, amount: data?.outstandingInvoiceAmount || 0 },
    { name: 'Overdue', value: data?.overdueInvoices || 0, amount: 0 }
  ];

  const isEmpty = !data || data.totalInvoices === 0;

  return (
    <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
      <h3 className="text-lg font-semibold text-gray-800 mb-4">Invoice Status Distribution</h3>
      {isEmpty && (
        <div className="text-center py-8 text-gray-500">
          <p>No invoice data available</p>
          <p className="text-sm">Load sample data to see invoice status breakdown</p>
        </div>
      )}
      <ResponsiveContainer width="100%" height={300}>
        <PieChart>
          <Pie
            data={chartData}
            cx="50%"
            cy="50%"
            labelLine={false}
            label={({ name, value }) => `${name}: ${value}`}
            outerRadius={80}
            fill="#8884d8"
            dataKey="value"
          >
            {chartData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
            ))}
          </Pie>
          <Tooltip />
          <Legend />
        </PieChart>
      </ResponsiveContainer>
    </div>
  );
};

export const InventoryValueChart: React.FC<{ data: any }> = ({ data }) => {
  const chartData = [
    {
      name: 'Inventory Overview',
      'Total Items': data?.inventory?.totalItems || 0,
      'Low Stock Items': data?.inventory?.lowStockItems || 0,
      'Total Value ($)': (data?.inventory?.totalValue || 0) / 1000, // Convert to thousands
    }
  ];

  const isEmpty = !data?.inventory || data.inventory.totalItems === 0;

  return (
    <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
      <h3 className="text-lg font-semibold text-gray-800 mb-4">Inventory Overview</h3>
      {isEmpty && (
        <div className="text-center py-8 text-gray-500">
          <p>No inventory data available</p>
          <p className="text-sm">Load sample data to see inventory metrics</p>
        </div>
      )}
      <ResponsiveContainer width="100%" height={300}>
        <BarChart data={chartData}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="name" />
          <YAxis />
          <Tooltip 
            formatter={(value, name) => {
              if (name === 'Total Value ($)') {
                return [`$${(Number(value) * 1000).toLocaleString()}`, 'Total Value'];
              }
              return [value, name];
            }}
          />
          <Legend />
          <Bar dataKey="Total Items" fill="#8884D8" />
          <Bar dataKey="Low Stock Items" fill="#FF8042" />
          <Bar dataKey="Total Value ($)" fill="#00C49F" />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

export const ProductionEfficiencyChart: React.FC<{ data: any }> = ({ data }) => {
  const chartData = [
    {
      name: 'Production Metrics',
      'Active Orders': data?.production?.activeOrders || 0,
      'Completed Orders': data?.production?.completedOrders || 0,
      'Efficiency %': data?.production?.efficiency || 0,
    }
  ];

  const isEmpty = !data?.production || data.production.totalOrders === 0;

  return (
    <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
      <h3 className="text-lg font-semibold text-gray-800 mb-4">Production Performance</h3>
      {isEmpty && (
        <div className="text-center py-8 text-gray-500">
          <p>No production data available</p>
          <p className="text-sm">Load sample data to see production metrics</p>
        </div>
      )}
      <ResponsiveContainer width="100%" height={300}>
        <BarChart data={chartData}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="name" />
          <YAxis />
          <Tooltip />
          <Legend />
          <Bar dataKey="Active Orders" fill="#3B82F6" />
          <Bar dataKey="Completed Orders" fill="#10B981" />
          <Bar dataKey="Efficiency %" fill="#F59E0B" />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

export const RecentTransactionsChart: React.FC<{ data: any }> = ({ data }) => {
  const transactions = data?.recentTransactions || [];
  
  const chartData = transactions.slice(0, 5).map((transaction: any, index: number) => ({
    name: `T${index + 1}`,
    Debit: transaction.debit,
    Credit: transaction.credit,
    description: transaction.description,
    account: transaction.accountName
  }));

  const isEmpty = !transactions || transactions.length === 0;

  return (
    <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
      <h3 className="text-lg font-semibold text-gray-800 mb-4">Recent Transactions</h3>
      {isEmpty && (
        <div className="text-center py-8 text-gray-500">
          <p>No transaction data available</p>
          <p className="text-sm">Load sample data to see recent transaction trends</p>
        </div>
      )}
      <ResponsiveContainer width="100%" height={300}>
        <BarChart data={chartData}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="name" />
          <YAxis />
          <Tooltip 
            formatter={(value, name) => [`$${Number(value).toLocaleString()}`, name]}
            labelFormatter={(label) => {
              const transaction = chartData.find(t => t.name === label);
              return transaction ? `${transaction.account}: ${transaction.description}` : label;
            }}
          />
          <Legend />
          <Bar dataKey="Debit" fill="#EF4444" />
          <Bar dataKey="Credit" fill="#10B981" />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

export const HRMetricsChart: React.FC<{ data: any }> = ({ data }) => {
  const chartData = [
    {
      name: 'HR Overview',
      'Active Employees': data?.hr?.activeEmployees || 0,
      'Total Employees': data?.hr?.totalEmployees || 0,
      'Suppliers': data?.procurement?.totalSuppliers || 0,
    }
  ];

  const isEmpty = !data?.hr || data.hr.totalEmployees === 0;

  return (
    <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
      <h3 className="text-lg font-semibold text-gray-800 mb-4">HR & Procurement Metrics</h3>
      {isEmpty && (
        <div className="text-center py-8 text-gray-500">
          <p>No HR data available</p>
          <p className="text-sm">Load sample data to see employee and supplier metrics</p>
        </div>
      )}
      <ResponsiveContainer width="100%" height={300}>
        <AreaChart data={chartData}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="name" />
          <YAxis />
          <Tooltip />
          <Legend />
          <Area type="monotone" dataKey="Active Employees" stackId="1" stroke="#8884D8" fill="#8884D8" />
          <Area type="monotone" dataKey="Total Employees" stackId="2" stroke="#82CA9D" fill="#82CA9D" />
          <Area type="monotone" dataKey="Suppliers" stackId="3" stroke="#FFC658" fill="#FFC658" />
        </AreaChart>
      </ResponsiveContainer>
    </div>
  );
};

const FinancialCharts: React.FC<FinancialChartsProps> = ({ data }) => {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <RevenueExpenseChart data={data} />
        <AssetLiabilityChart data={data} />
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <InvoiceStatusChart data={data} />
        <InventoryValueChart data={data} />
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <ProductionEfficiencyChart data={data} />
        <HRMetricsChart data={data} />
      </div>
      
      <RecentTransactionsChart data={data} />
    </div>
  );
};

export default FinancialCharts;
