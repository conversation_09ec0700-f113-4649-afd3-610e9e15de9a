import React, { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import Sidebar from "../components/sidebar";
import Navbar from "../components/navbar";

function PhilippineGAAP() {
  const [activeTab, setActiveTab] = useState("revenue-recognition");
  const [showRevenueModal, setShowRevenueModal] = useState(false);
  const [showDepreciationModal, setShowDepreciationModal] = useState(false);
  const [showVATModal, setShowVATModal] = useState(false);

  // Queries
  const revenueStatus = useQuery(api.philippineGAAP.getRevenueRecognitionStatus);
  const vatSummary = useQuery(api.philippineGAAP.getVATSummaryReport, {
    startDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0]
  });

  // Mutations
  const recognizeRevenue = useMutation(api.philippineGAAP.recognizeRevenue);
  const calculateDepreciation = useMutation(api.philippineGAAP.calculateMonthlyDepreciation);
  const calculateSalesVAT = useMutation(api.philippineGAAP.calculateSalesVAT);

  const [revenueForm, setRevenueForm] = useState({
    invoiceId: "",
    performanceObligationId: "",
    recognitionDate: new Date().toISOString().split('T')[0],
    recognitionAmount: 0,
    recognitionMethod: "Point in Time" as "Point in Time" | "Over Time",
    percentageComplete: 100
  });

  const [depreciationForm, setDepreciationForm] = useState({
    periodDate: new Date().toISOString().slice(0, 7) // YYYY-MM format
  });

  const handleRecognizeRevenue = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const result = await recognizeRevenue(revenueForm);
      alert(`✅ ${result.message}\n\nRevenue Recognized: ₱${result.revenueRecognized.toLocaleString()}\nTotal Recognized: ₱${result.totalRecognized.toLocaleString()}\nRemaining: ₱${result.remainingToRecognize.toLocaleString()}`);
      setShowRevenueModal(false);
    } catch (error) {
      alert("Error recognizing revenue: " + String(error));
    }
  };

  const handleCalculateDepreciation = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const result = await calculateDepreciation(depreciationForm);
      alert(`✅ ${result.message}\n\nTotal Depreciation: ₱${result.totalDepreciation.toLocaleString()}\nAssets Depreciated: ${result.assetsDepreciated}\nPeriod: ${result.period}`);
      setShowDepreciationModal(false);
    } catch (error) {
      alert("Error calculating depreciation: " + String(error));
    }
  };

  if (!revenueStatus || !vatSummary) {
    return (
      <div className="flex h-screen bg-gradient-to-br from-slate-50 to-blue-50">
        <Sidebar />
        <div className="flex-1 flex flex-col">
          <Navbar />
          <main className="p-8 flex-1 overflow-auto">
            <div className="max-w-7xl mx-auto">
              <div className="text-center py-16">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-6">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>
                <h1 className="text-3xl font-bold text-gray-900 mb-4">Philippine GAAP Compliance</h1>
                <p className="text-lg text-gray-600">Loading compliance data...</p>
              </div>
            </div>
          </main>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      <Sidebar />
      <div className="flex-1 flex flex-col">
        <Navbar />
        <main className="p-8 flex-1 overflow-auto">
          <div className="max-w-7xl mx-auto">
            {/* Header */}
            <div className="mb-8">
              <h1 className="text-4xl font-bold text-gray-900 mb-2">
                Philippine GAAP Compliance Center
              </h1>
              <p className="text-lg text-gray-600">
                PFRS 15 Revenue Recognition • PAS 2 Inventory • PAS 16 Assets • BIR Tax Compliance
              </p>
            </div>

            {/* Navigation Tabs */}
            <div className="flex flex-wrap gap-3 mb-8">
              <button
                onClick={() => setActiveTab("revenue-recognition")}
                className={`inline-flex items-center px-6 py-3 rounded-xl font-semibold transition-all duration-200 shadow-sm ${
                  activeTab === "revenue-recognition"
                    ? "bg-blue-600 text-white shadow-lg shadow-blue-200 transform scale-105"
                    : "bg-white text-gray-700 border border-gray-200 hover:bg-blue-50 hover:border-blue-300"
                }`}
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                PFRS 15 Revenue
              </button>
              <button
                onClick={() => setActiveTab("vat-compliance")}
                className={`inline-flex items-center px-6 py-3 rounded-xl font-semibold transition-all duration-200 shadow-sm ${
                  activeTab === "vat-compliance"
                    ? "bg-green-600 text-white shadow-lg shadow-green-200 transform scale-105"
                    : "bg-white text-gray-700 border border-gray-200 hover:bg-green-50 hover:border-green-300"
                }`}
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                </svg>
                BIR VAT Compliance
              </button>
              <button
                onClick={() => setActiveTab("asset-depreciation")}
                className={`inline-flex items-center px-6 py-3 rounded-xl font-semibold transition-all duration-200 shadow-sm ${
                  activeTab === "asset-depreciation"
                    ? "bg-purple-600 text-white shadow-lg shadow-purple-200 transform scale-105"
                    : "bg-white text-gray-700 border border-gray-200 hover:bg-purple-50 hover:border-purple-300"
                }`}
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
                PAS 16 Assets
              </button>
              <button
                onClick={() => setActiveTab("inventory-costing")}
                className={`inline-flex items-center px-6 py-3 rounded-xl font-semibold transition-all duration-200 shadow-sm ${
                  activeTab === "inventory-costing"
                    ? "bg-orange-600 text-white shadow-lg shadow-orange-200 transform scale-105"
                    : "bg-white text-gray-700 border border-gray-200 hover:bg-orange-50 hover:border-orange-300"
                }`}
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                </svg>
                PAS 2 Inventory
              </button>
            </div>

            {/* Content based on active tab */}
            {activeTab === "revenue-recognition" && (
              <div className="space-y-8">
                {/* Revenue Recognition Summary */}
                <div className="bg-white rounded-2xl shadow-xl border border-gray-200 p-8">
                  <div className="flex items-center justify-between mb-6">
                    <h2 className="text-2xl font-bold text-gray-900">PFRS 15 Revenue Recognition Status</h2>
                    <button
                      onClick={() => setShowRevenueModal(true)}
                      className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                      Recognize Revenue
                    </button>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-6 border border-blue-200">
                      <div className="text-3xl font-bold text-blue-700 mb-2">{revenueStatus.summary.totalInvoices}</div>
                      <div className="text-sm font-medium text-blue-600">Total Invoices</div>
                    </div>
                    <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-xl p-6 border border-green-200">
                      <div className="text-3xl font-bold text-green-700 mb-2">{revenueStatus.summary.fullyRecognized}</div>
                      <div className="text-sm font-medium text-green-600">Fully Recognized</div>
                    </div>
                    <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-xl p-6 border border-yellow-200">
                      <div className="text-3xl font-bold text-yellow-700 mb-2">{revenueStatus.summary.partiallyRecognized}</div>
                      <div className="text-sm font-medium text-yellow-600">Partially Recognized</div>
                    </div>
                    <div className="bg-gradient-to-br from-red-50 to-red-100 rounded-xl p-6 border border-red-200">
                      <div className="text-3xl font-bold text-red-700 mb-2">{revenueStatus.summary.notRecognized}</div>
                      <div className="text-sm font-medium text-red-600">Not Recognized</div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="bg-gray-50 rounded-xl p-6">
                      <div className="text-lg font-semibold text-gray-700 mb-2">Total Contract Value</div>
                      <div className="text-2xl font-bold text-gray-900">₱{revenueStatus.summary.totalContractValue.toLocaleString()}</div>
                    </div>
                    <div className="bg-gray-50 rounded-xl p-6">
                      <div className="text-lg font-semibold text-gray-700 mb-2">Recognized Revenue</div>
                      <div className="text-2xl font-bold text-green-600">₱{revenueStatus.summary.totalRecognizedRevenue.toLocaleString()}</div>
                    </div>
                    <div className="bg-gray-50 rounded-xl p-6">
                      <div className="text-lg font-semibold text-gray-700 mb-2">Deferred Revenue</div>
                      <div className="text-2xl font-bold text-orange-600">₱{revenueStatus.summary.totalDeferredRevenue.toLocaleString()}</div>
                    </div>
                  </div>
                </div>

                {/* Revenue Recognition Table */}
                <div className="bg-white rounded-2xl shadow-xl border border-gray-200 p-8">
                  <h3 className="text-xl font-bold text-gray-900 mb-6">Invoice Revenue Recognition Details</h3>
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="bg-gradient-to-r from-gray-100 to-gray-200 border-b-2 border-gray-300">
                          <th className="text-left py-4 px-6 font-bold text-gray-800 text-sm uppercase tracking-wider">Invoice</th>
                          <th className="text-left py-4 px-6 font-bold text-gray-800 text-sm uppercase tracking-wider">Customer</th>
                          <th className="text-right py-4 px-6 font-bold text-gray-800 text-sm uppercase tracking-wider">Total Amount</th>
                          <th className="text-right py-4 px-6 font-bold text-gray-800 text-sm uppercase tracking-wider">Recognized</th>
                          <th className="text-right py-4 px-6 font-bold text-gray-800 text-sm uppercase tracking-wider">Remaining</th>
                          <th className="text-center py-4 px-6 font-bold text-gray-800 text-sm uppercase tracking-wider">Status</th>
                          <th className="text-center py-4 px-6 font-bold text-gray-800 text-sm uppercase tracking-wider">Progress</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {revenueStatus.invoices.map((invoice, index) => (
                          <tr key={index} className="hover:bg-blue-50 transition-all duration-200">
                            <td className="py-4 px-6">
                              <div className="font-semibold text-gray-900">{invoice.invoiceNumber}</div>
                              <div className="text-sm text-gray-500">{new Date(invoice.invoiceDate).toLocaleDateString()}</div>
                            </td>
                            <td className="py-4 px-6">
                              <div className="font-medium text-gray-900">{invoice.customerName}</div>
                            </td>
                            <td className="py-4 px-6 text-right">
                              <div className="font-semibold text-gray-900">₱{invoice.totalAmount.toLocaleString()}</div>
                            </td>
                            <td className="py-4 px-6 text-right">
                              <div className="font-semibold text-green-600">₱{invoice.recognizedAmount.toLocaleString()}</div>
                            </td>
                            <td className="py-4 px-6 text-right">
                              <div className="font-semibold text-orange-600">₱{invoice.remainingToRecognize.toLocaleString()}</div>
                            </td>
                            <td className="py-4 px-6 text-center">
                              <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold ${
                                invoice.revenueRecognitionStatus === "Fully Recognized" 
                                  ? "bg-green-100 text-green-800"
                                  : invoice.revenueRecognitionStatus === "Partially Recognized"
                                  ? "bg-yellow-100 text-yellow-800"
                                  : "bg-red-100 text-red-800"
                              }`}>
                                {invoice.revenueRecognitionStatus || "Not Recognized"}
                              </span>
                            </td>
                            <td className="py-4 px-6 text-center">
                              <div className="flex items-center justify-center">
                                <div className="w-16 bg-gray-200 rounded-full h-2">
                                  <div 
                                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                                    style={{ width: `${invoice.recognitionPercentage}%` }}
                                  ></div>
                                </div>
                                <span className="ml-2 text-sm font-medium text-gray-600">
                                  {Math.round(invoice.recognitionPercentage)}%
                                </span>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            )}

            {/* VAT Compliance Tab */}
            {activeTab === "vat-compliance" && (
              <div className="space-y-8">
                {/* VAT Summary */}
                <div className="bg-white rounded-2xl shadow-xl border border-gray-200 p-8">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">BIR VAT Compliance Summary</h2>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    {/* Sales VAT */}
                    <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-xl p-6 border border-green-200">
                      <h3 className="text-lg font-bold text-green-800 mb-4">Sales VAT (Output)</h3>
                      <div className="space-y-3">
                        <div className="flex justify-between">
                          <span className="text-green-700">Vatable Sales:</span>
                          <span className="font-semibold text-green-800">₱{vatSummary.salesSummary.totalVatableSales.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-green-700">VAT Exempt:</span>
                          <span className="font-semibold text-green-800">₱{vatSummary.salesSummary.totalVatExemptSales.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-green-700">Zero Rated:</span>
                          <span className="font-semibold text-green-800">₱{vatSummary.salesSummary.totalZeroRatedSales.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between border-t border-green-300 pt-3">
                          <span className="text-green-700 font-bold">Output VAT:</span>
                          <span className="font-bold text-green-800">₱{vatSummary.salesSummary.totalOutputVAT.toLocaleString()}</span>
                        </div>
                      </div>
                    </div>

                    {/* Purchase VAT */}
                    <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-6 border border-blue-200">
                      <h3 className="text-lg font-bold text-blue-800 mb-4">Purchase VAT (Input)</h3>
                      <div className="space-y-3">
                        <div className="flex justify-between">
                          <span className="text-blue-700">Vatable Purchases:</span>
                          <span className="font-semibold text-blue-800">₱{vatSummary.purchaseSummary.totalVatablePurchases.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-blue-700">VAT Exempt:</span>
                          <span className="font-semibold text-blue-800">₱{vatSummary.purchaseSummary.totalVatExemptPurchases.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-blue-700">Zero Rated:</span>
                          <span className="font-semibold text-blue-800">₱{vatSummary.purchaseSummary.totalZeroRatedPurchases.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between border-t border-blue-300 pt-3">
                          <span className="text-blue-700 font-bold">Input VAT:</span>
                          <span className="font-bold text-blue-800">₱{vatSummary.purchaseSummary.totalInputVAT.toLocaleString()}</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* VAT Position */}
                  <div className="mt-8 bg-gradient-to-r from-purple-50 to-purple-100 rounded-xl p-6 border border-purple-200">
                    <h3 className="text-lg font-bold text-purple-800 mb-4">VAT Position</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-purple-700">₱{vatSummary.vatPayable.toLocaleString()}</div>
                        <div className="text-sm text-purple-600">VAT Payable</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-purple-700">₱{vatSummary.vatRefundable.toLocaleString()}</div>
                        <div className="text-sm text-purple-600">VAT Refundable</div>
                      </div>
                      <div className="text-center">
                        <div className={`text-2xl font-bold ${vatSummary.netVATPosition >= 0 ? 'text-red-600' : 'text-green-600'}`}>
                          ₱{Math.abs(vatSummary.netVATPosition).toLocaleString()}
                        </div>
                        <div className="text-sm text-purple-600">
                          {vatSummary.netVATPosition >= 0 ? 'Net Payable' : 'Net Refundable'}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Asset Depreciation Tab */}
            {activeTab === "asset-depreciation" && (
              <div className="space-y-8">
                <div className="bg-white rounded-2xl shadow-xl border border-gray-200 p-8">
                  <div className="flex items-center justify-between mb-6">
                    <h2 className="text-2xl font-bold text-gray-900">PAS 16 Asset Depreciation</h2>
                    <button
                      type="button"
                      onClick={() => setShowDepreciationModal(true)}
                      className="inline-flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                    >
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                      Calculate Depreciation
                    </button>
                  </div>

                  <div className="bg-gradient-to-r from-purple-50 to-purple-100 rounded-xl p-6 border border-purple-200">
                    <p className="text-purple-800 text-center">
                      <strong>PAS 16 Compliance:</strong> Automated monthly depreciation calculation with proper journal entries.
                      Supports straight-line, declining balance, and component depreciation methods.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Inventory Costing Tab */}
            {activeTab === "inventory-costing" && (
              <div className="space-y-8">
                <div className="bg-white rounded-2xl shadow-xl border border-gray-200 p-8">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">PAS 2 Inventory Costing</h2>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div className="bg-gradient-to-br from-orange-50 to-orange-100 rounded-xl p-6 border border-orange-200">
                      <h3 className="text-lg font-bold text-orange-800 mb-4">FIFO Method</h3>
                      <p className="text-orange-700 text-sm">
                        First-In, First-Out costing method. Inventory is valued using the cost of the most recent purchases,
                        while cost of goods sold uses the cost of the oldest inventory.
                      </p>
                    </div>

                    <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-xl p-6 border border-yellow-200">
                      <h3 className="text-lg font-bold text-yellow-800 mb-4">Weighted Average</h3>
                      <p className="text-yellow-700 text-sm">
                        Weighted average costing method. The cost of inventory is calculated as the weighted average
                        of all units available for sale during the period.
                      </p>
                    </div>
                  </div>

                  <div className="mt-8 bg-gradient-to-r from-red-50 to-red-100 rounded-xl p-6 border border-red-200">
                    <h3 className="text-lg font-bold text-red-800 mb-2">⚠️ PFRS Compliance Note</h3>
                    <p className="text-red-700 text-sm">
                      <strong>LIFO (Last-In, First-Out) is NOT permitted</strong> under Philippine Financial Reporting Standards (PFRS).
                      Only FIFO and Weighted Average methods are allowed for inventory costing.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Depreciation Modal */}
            {showDepreciationModal && (
              <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
                <div className="bg-white rounded-xl shadow-2xl p-6 w-full max-w-lg border border-gray-200">
                  <div className="flex items-center justify-between mb-6">
                    <h3 className="text-xl font-bold text-gray-900">Calculate Monthly Depreciation</h3>
                    <button
                      type="button"
                      onClick={() => setShowDepreciationModal(false)}
                      className="text-gray-400 hover:text-gray-600 transition-colors"
                      aria-label="Close modal"
                      title="Close modal"
                    >
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>

                  <form onSubmit={handleCalculateDepreciation}>
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-semibold text-gray-700 mb-2">Period (YYYY-MM)</label>
                        <input
                          type="text"
                          pattern="[0-9]{4}-[0-9]{2}"
                          placeholder="YYYY-MM (e.g., 2024-01)"
                          value={depreciationForm.periodDate}
                          onChange={(e) => setDepreciationForm({...depreciationForm, periodDate: e.target.value})}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                          required
                        />
                      </div>

                      <div className="bg-purple-50 rounded-lg p-4 border border-purple-200">
                        <h4 className="font-semibold text-purple-800 mb-2">PAS 16 Depreciation Process</h4>
                        <ul className="text-sm text-purple-700 space-y-1">
                          <li>• Calculates depreciation for all active fixed assets</li>
                          <li>• Creates automatic journal entries (Debit: Depreciation Expense, Credit: Accumulated Depreciation)</li>
                          <li>• Updates asset book values and depreciation schedules</li>
                          <li>• Ensures compliance with PAS 16 requirements</li>
                        </ul>
                      </div>
                    </div>

                    <div className="flex justify-end space-x-3 mt-8 pt-6 border-t border-gray-200">
                      <button
                        type="button"
                        onClick={() => setShowDepreciationModal(false)}
                        className="px-6 py-3 text-gray-700 bg-gray-100 border border-gray-300 rounded-lg hover:bg-gray-200 transition-all duration-200 font-medium"
                      >
                        Cancel
                      </button>
                      <button
                        type="submit"
                        className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-all duration-200 font-medium shadow-lg hover:shadow-xl"
                      >
                        Calculate Depreciation
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            )}

            {/* Revenue Recognition Modal */}
            {showRevenueModal && (
              <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
                <div className="bg-white rounded-xl shadow-2xl p-6 w-full max-w-lg border border-gray-200">
                  <div className="flex items-center justify-between mb-6">
                    <h3 className="text-xl font-bold text-gray-900">Recognize Revenue (PFRS 15)</h3>
                    <button
                      type="button"
                      onClick={() => setShowRevenueModal(false)}
                      className="text-gray-400 hover:text-gray-600 transition-colors"
                      aria-label="Close modal"
                      title="Close modal"
                    >
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>

                  <form onSubmit={handleRecognizeRevenue}>
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-semibold text-gray-700 mb-2">Invoice ID</label>
                        <input
                          type="text"
                          value={revenueForm.invoiceId}
                          onChange={(e) => setRevenueForm({...revenueForm, invoiceId: e.target.value})}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="Enter invoice ID"
                          required
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-semibold text-gray-700 mb-2">Performance Obligation ID</label>
                        <input
                          type="text"
                          value={revenueForm.performanceObligationId}
                          onChange={(e) => setRevenueForm({...revenueForm, performanceObligationId: e.target.value})}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="Enter performance obligation ID"
                          required
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-semibold text-gray-700 mb-2">Recognition Amount</label>
                        <input
                          type="number"
                          step="0.01"
                          value={revenueForm.recognitionAmount}
                          onChange={(e) => setRevenueForm({...revenueForm, recognitionAmount: Number(e.target.value)})}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="0.00"
                          required
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-semibold text-gray-700 mb-2">Recognition Date</label>
                        <input
                          type="date"
                          value={revenueForm.recognitionDate}
                          onChange={(e) => setRevenueForm({...revenueForm, recognitionDate: e.target.value})}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                          required
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-semibold text-gray-700 mb-2">Recognition Method</label>
                        <select
                          value={revenueForm.recognitionMethod}
                          onChange={(e) => setRevenueForm({...revenueForm, recognitionMethod: e.target.value as "Point in Time" | "Over Time"})}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                          <option value="Point in Time">Point in Time</option>
                          <option value="Over Time">Over Time</option>
                        </select>
                      </div>
                    </div>

                    <div className="flex justify-end space-x-3 mt-8 pt-6 border-t border-gray-200">
                      <button
                        type="button"
                        onClick={() => setShowRevenueModal(false)}
                        className="px-6 py-3 text-gray-700 bg-gray-100 border border-gray-300 rounded-lg hover:bg-gray-200 transition-all duration-200 font-medium"
                      >
                        Cancel
                      </button>
                      <button
                        type="submit"
                        className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all duration-200 font-medium shadow-lg hover:shadow-xl"
                      >
                        Recognize Revenue
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            )}
          </div>
        </main>
      </div>
    </div>
  );
}

export default PhilippineGAAP;
