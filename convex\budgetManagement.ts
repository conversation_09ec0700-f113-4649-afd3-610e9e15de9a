import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

// ============================================================================
// BUDGET MANAGEMENT AND VARIANCE ANALYSIS SYSTEM
// Budget vs Actual • Cost Center Analysis • Forecast Management
// ============================================================================

// Create Budget
export const createBudget = mutation({
  args: {
    budgetName: v.string(),
    fiscalYear: v.number(),
    budgetType: v.union(v.literal("Operating"), v.literal("Capital"), v.literal("Cash Flow")),
    totalBudgetAmount: v.number(),
    notes: v.optional(v.string())
  },
  handler: async (ctx, args) => {
    const budgetId = await ctx.db.insert("budgets", {
      budgetName: args.budgetName,
      fiscalYear: args.fiscalYear,
      budgetType: args.budgetType,
      status: "Draft",
      totalBudgetAmount: args.totalBudgetAmount,
      notes: args.notes
    });

    return {
      success: true,
      budgetId,
      message: `✅ Budget created successfully\n\n📋 Budget Details:\n• Name: ${args.budgetName}\n• Fiscal Year: ${args.fiscalYear}\n• Type: ${args.budgetType}\n• Total Amount: ₱${args.totalBudgetAmount.toLocaleString()}\n• Status: Draft`,
      budgetName: args.budgetName,
      fiscalYear: args.fiscalYear,
      budgetType: args.budgetType,
      totalBudgetAmount: args.totalBudgetAmount,
      status: "Draft"
    };
  }
});

// Add Budget Line Items
export const addBudgetLineItems = mutation({
  args: {
    budgetId: v.id("budgets"),
    lineItems: v.array(v.object({
      accountCode: v.string(),
      departmentId: v.optional(v.string()),
      costCenterId: v.optional(v.string()),
      budgetedAmount: v.number(),
      period: v.string(), // e.g., "2024-01", "2024-Q1", "2024"
      notes: v.optional(v.string())
    }))
  },
  handler: async (ctx, args) => {
    const budget = await ctx.db.get(args.budgetId);
    if (!budget) {
      throw new Error("Budget not found");
    }

    if (budget.status === "Closed") {
      throw new Error("Cannot modify closed budget");
    }

    // Get accounts to validate account codes
    const accounts = await ctx.db.query("accounts").collect();
    const accountMap = new Map(accounts.map(acc => [acc.code, acc]));

    const createdLineItems = [];
    let totalBudgetAmount = 0;

    for (const item of args.lineItems) {
      const account = accountMap.get(item.accountCode);
      if (!account) {
        throw new Error(`Account with code ${item.accountCode} not found`);
      }

      const lineItemId = await ctx.db.insert("budgetLineItems", {
        budgetId: args.budgetId,
        accountId: account._id,
        departmentId: item.departmentId,
        costCenterId: item.costCenterId,
        budgetedAmount: item.budgetedAmount,
        period: item.period,
        notes: item.notes
      });

      createdLineItems.push({
        lineItemId,
        accountCode: item.accountCode,
        accountName: account.name,
        budgetedAmount: item.budgetedAmount,
        period: item.period
      });

      totalBudgetAmount += item.budgetedAmount;
    }

    // Update budget total
    await ctx.db.patch(args.budgetId, {
      totalBudgetAmount: totalBudgetAmount
    });

    return {
      success: true,
      message: `✅ Budget line items added successfully\n\n📋 Summary:\n• Line Items Added: ${createdLineItems.length}\n• Total Budget Amount: ₱${totalBudgetAmount.toLocaleString()}\n• Budget: ${budget.budgetName}`,
      budgetId: args.budgetId,
      lineItemsAdded: createdLineItems.length,
      totalBudgetAmount,
      createdLineItems
    };
  }
});

// Approve Budget
export const approveBudget = mutation({
  args: {
    budgetId: v.id("budgets"),
    approvedBy: v.string()
  },
  handler: async (ctx, args) => {
    const budget = await ctx.db.get(args.budgetId);
    if (!budget) {
      throw new Error("Budget not found");
    }

    if (budget.status !== "Draft") {
      throw new Error("Only draft budgets can be approved");
    }

    await ctx.db.patch(args.budgetId, {
      status: "Approved",
      approvedBy: args.approvedBy,
      approvedDate: new Date().toISOString()
    });

    return {
      success: true,
      message: `✅ Budget approved successfully\n\n📋 Approval Details:\n• Budget: ${budget.budgetName}\n• Approved By: ${args.approvedBy}\n• Total Amount: ₱${budget.totalBudgetAmount.toLocaleString()}\n• Status: Approved`,
      budgetName: budget.budgetName,
      approvedBy: args.approvedBy,
      totalBudgetAmount: budget.totalBudgetAmount,
      status: "Approved"
    };
  }
});

// Update Actual Amounts (from journal entries)
export const updateBudgetActuals = mutation({
  args: {
    budgetId: v.id("budgets"),
    period: v.string() // e.g., "2024-01", "2024-Q1", "2024"
  },
  handler: async (ctx, args) => {
    const budget = await ctx.db.get(args.budgetId);
    if (!budget) {
      throw new Error("Budget not found");
    }

    // Get budget line items for this period
    const budgetLineItems = await ctx.db.query("budgetLineItems")
      .filter(q => q.and(
        q.eq(q.field("budgetId"), args.budgetId),
        q.eq(q.field("period"), args.period)
      ))
      .collect();

    if (budgetLineItems.length === 0) {
      throw new Error(`No budget line items found for period ${args.period}`);
    }

    // Determine date range based on period format
    let startDate: string, endDate: string;
    
    if (args.period.includes('-Q')) {
      // Quarterly period (e.g., "2024-Q1")
      const [year, quarter] = args.period.split('-Q');
      const quarterNum = parseInt(quarter);
      const startMonth = (quarterNum - 1) * 3 + 1;
      const endMonth = quarterNum * 3;
      startDate = `${year}-${String(startMonth).padStart(2, '0')}-01`;
      endDate = new Date(parseInt(year), endMonth, 0).toISOString().split('T')[0];
    } else if (args.period.includes('-')) {
      // Monthly period (e.g., "2024-01")
      const [year, month] = args.period.split('-');
      startDate = `${year}-${month}-01`;
      endDate = new Date(parseInt(year), parseInt(month), 0).toISOString().split('T')[0];
    } else {
      // Annual period (e.g., "2024")
      startDate = `${args.period}-01-01`;
      endDate = `${args.period}-12-31`;
    }

    // Get journal entries for the period
    const journalEntries = await ctx.db.query("journalEntries")
      .filter(q => q.and(
        q.gte(q.field("date"), startDate),
        q.lte(q.field("date"), endDate),
        q.eq(q.field("isPosted"), true)
      ))
      .collect();

    // Calculate actual amounts by account
    const actualAmounts = new Map<string, number>();
    
    journalEntries.forEach(entry => {
      const currentAmount = actualAmounts.get(entry.accountId) || 0;
      // For expense accounts, use debit amounts; for revenue accounts, use credit amounts
      actualAmounts.set(entry.accountId, currentAmount + entry.debit - entry.credit);
    });

    // Update budget line items with actual amounts
    let updatedItems = 0;
    let totalVariance = 0;

    for (const lineItem of budgetLineItems) {
      const actualAmount = Math.abs(actualAmounts.get(lineItem.accountId) || 0);
      const variance = actualAmount - lineItem.budgetedAmount;
      const variancePercent = lineItem.budgetedAmount > 0 ? (variance / lineItem.budgetedAmount) * 100 : 0;

      await ctx.db.patch(lineItem._id, {
        actualAmount,
        variance,
        variancePercent
      });

      totalVariance += Math.abs(variance);
      updatedItems++;
    }

    return {
      success: true,
      message: `✅ Budget actuals updated successfully\n\n📋 Update Summary:\n• Period: ${args.period}\n• Line Items Updated: ${updatedItems}\n• Total Variance: ₱${totalVariance.toLocaleString()}\n• Budget: ${budget.budgetName}`,
      budgetId: args.budgetId,
      period: args.period,
      updatedItems,
      totalVariance,
      dateRange: { startDate, endDate }
    };
  }
});

// Get Budget vs Actual Report
export const getBudgetVsActualReport = query({
  args: {
    budgetId: v.optional(v.id("budgets")),
    fiscalYear: v.optional(v.number()),
    period: v.optional(v.string()),
    departmentId: v.optional(v.string()),
    costCenterId: v.optional(v.string())
  },
  handler: async (ctx, args) => {
    // Get budgets
    let budgetsQuery = ctx.db.query("budgets");
    if (args.budgetId) {
      const budget = await ctx.db.get(args.budgetId);
      if (!budget) {
        throw new Error("Budget not found");
      }
      const budgets = [budget];
      
      // Get budget line items
      let lineItemsQuery = ctx.db.query("budgetLineItems")
        .filter(q => q.eq(q.field("budgetId"), args.budgetId));
      
      if (args.period) {
        lineItemsQuery = lineItemsQuery.filter(q => q.eq(q.field("period"), args.period));
      }
      if (args.departmentId) {
        lineItemsQuery = lineItemsQuery.filter(q => q.eq(q.field("departmentId"), args.departmentId));
      }
      if (args.costCenterId) {
        lineItemsQuery = lineItemsQuery.filter(q => q.eq(q.field("costCenterId"), args.costCenterId));
      }

      const budgetLineItems = await lineItemsQuery.collect();

      // Get account details
      const accountIds = [...new Set(budgetLineItems.map(item => item.accountId))];
      const accounts = await Promise.all(accountIds.map(id => ctx.db.get(id)));
      const accountMap = new Map(accounts.filter(Boolean).map(account => [account!._id, account]));

      // Prepare report data
      const reportData = budgetLineItems.map(item => {
        const account = accountMap.get(item.accountId);
        const variance = (item.actualAmount || 0) - item.budgetedAmount;
        const variancePercent = item.budgetedAmount > 0 ? (variance / item.budgetedAmount) * 100 : 0;
        const isUnfavorable = (account?.type === "Expense" && variance > 0) || 
                             (account?.type === "Revenue" && variance < 0);

        return {
          accountCode: account?.code || "Unknown",
          accountName: account?.name || "Unknown",
          accountType: account?.type || "Unknown",
          departmentId: item.departmentId,
          costCenterId: item.costCenterId,
          period: item.period,
          budgetedAmount: item.budgetedAmount,
          actualAmount: item.actualAmount || 0,
          variance,
          variancePercent,
          isUnfavorable,
          varianceStatus: Math.abs(variancePercent) <= 5 ? "On Target" : 
                         Math.abs(variancePercent) <= 15 ? "Acceptable" : "Significant"
        };
      });

      // Calculate summary
      const totalBudgeted = reportData.reduce((sum, item) => sum + item.budgetedAmount, 0);
      const totalActual = reportData.reduce((sum, item) => sum + item.actualAmount, 0);
      const totalVariance = totalActual - totalBudgeted;
      const totalVariancePercent = totalBudgeted > 0 ? (totalVariance / totalBudgeted) * 100 : 0;

      const favorableVariances = reportData.filter(item => !item.isUnfavorable && item.variance !== 0).length;
      const unfavorableVariances = reportData.filter(item => item.isUnfavorable).length;
      const onTargetItems = reportData.filter(item => item.varianceStatus === "On Target").length;

      return {
        reportTitle: "Budget vs Actual Report",
        companyName: "XYZ Manufacturing Ltd.",
        budget: {
          budgetName: budget.budgetName,
          fiscalYear: budget.fiscalYear,
          budgetType: budget.budgetType,
          status: budget.status
        },
        period: args.period || "All Periods",
        filters: {
          departmentId: args.departmentId,
          costCenterId: args.costCenterId
        },
        summary: {
          totalBudgeted,
          totalActual,
          totalVariance,
          totalVariancePercent,
          favorableVariances,
          unfavorableVariances,
          onTargetItems,
          totalLineItems: reportData.length,
          budgetUtilization: totalBudgeted > 0 ? (totalActual / totalBudgeted) * 100 : 0
        },
        reportData,
        currency: "PHP",
        generatedAt: new Date().toISOString()
      };
    } else {
      // Return summary of all budgets if no specific budget ID provided
      if (args.fiscalYear) {
        budgetsQuery = budgetsQuery.filter(q => q.eq(q.field("fiscalYear"), args.fiscalYear));
      }

      const budgets = await budgetsQuery.collect();

      const budgetSummary = budgets.map(budget => ({
        budgetId: budget._id,
        budgetName: budget.budgetName,
        fiscalYear: budget.fiscalYear,
        budgetType: budget.budgetType,
        status: budget.status,
        totalBudgetAmount: budget.totalBudgetAmount,
        approvedBy: budget.approvedBy,
        approvedDate: budget.approvedDate
      }));

      return {
        reportTitle: "Budget Summary",
        companyName: "XYZ Manufacturing Ltd.",
        fiscalYear: args.fiscalYear,
        budgetSummary,
        totalBudgets: budgets.length,
        totalBudgetAmount: budgets.reduce((sum, budget) => sum + budget.totalBudgetAmount, 0),
        currency: "PHP",
        generatedAt: new Date().toISOString()
      };
    }
  }
});

// Get Variance Analysis Report
export const getVarianceAnalysisReport = query({
  args: {
    budgetId: v.id("budgets"),
    period: v.string(),
    varianceThreshold: v.optional(v.number()) // Percentage threshold for significant variances
  },
  handler: async (ctx, args) => {
    const budget = await ctx.db.get(args.budgetId);
    if (!budget) {
      throw new Error("Budget not found");
    }

    const threshold = args.varianceThreshold || 10; // Default 10% threshold

    // Get budget line items with significant variances
    const budgetLineItems = await ctx.db.query("budgetLineItems")
      .filter(q => q.and(
        q.eq(q.field("budgetId"), args.budgetId),
        q.eq(q.field("period"), args.period)
      ))
      .collect();

    const significantVariances = budgetLineItems.filter(item => 
      Math.abs(item.variancePercent || 0) >= threshold
    );

    // Get account details
    const accountIds = [...new Set(significantVariances.map(item => item.accountId))];
    const accounts = await Promise.all(accountIds.map(id => ctx.db.get(id)));
    const accountMap = new Map(accounts.filter(Boolean).map(account => [account!._id, account]));

    // Analyze variances
    const varianceAnalysis = significantVariances.map(item => {
      const account = accountMap.get(item.accountId);
      const variance = item.variance || 0;
      const variancePercent = item.variancePercent || 0;
      const isUnfavorable = (account?.type === "Expense" && variance > 0) || 
                           (account?.type === "Revenue" && variance < 0);

      let analysis = "";
      if (account?.type === "Revenue") {
        analysis = variance > 0 ? "Revenue exceeded budget - positive performance" : 
                  "Revenue below budget - investigate sales performance";
      } else if (account?.type === "Expense") {
        analysis = variance > 0 ? "Expenses exceeded budget - cost control needed" : 
                  "Expenses below budget - efficient cost management";
      }

      return {
        accountCode: account?.code || "Unknown",
        accountName: account?.name || "Unknown",
        accountType: account?.type || "Unknown",
        budgetedAmount: item.budgetedAmount,
        actualAmount: item.actualAmount || 0,
        variance,
        variancePercent,
        isUnfavorable,
        severityLevel: Math.abs(variancePercent) >= 25 ? "High" : 
                      Math.abs(variancePercent) >= 15 ? "Medium" : "Low",
        analysis,
        recommendedAction: isUnfavorable ? 
          (account?.type === "Expense" ? "Review cost controls and spending authorization" : 
           "Analyze revenue drivers and market conditions") :
          "Monitor to ensure trend continues"
      };
    });

    // Summary statistics
    const totalUnfavorableVariance = varianceAnalysis
      .filter(item => item.isUnfavorable)
      .reduce((sum, item) => sum + Math.abs(item.variance), 0);

    const totalFavorableVariance = varianceAnalysis
      .filter(item => !item.isUnfavorable)
      .reduce((sum, item) => sum + Math.abs(item.variance), 0);

    const highSeverityCount = varianceAnalysis.filter(item => item.severityLevel === "High").length;
    const mediumSeverityCount = varianceAnalysis.filter(item => item.severityLevel === "Medium").length;

    return {
      reportTitle: "Variance Analysis Report",
      companyName: "XYZ Manufacturing Ltd.",
      budget: {
        budgetName: budget.budgetName,
        fiscalYear: budget.fiscalYear,
        budgetType: budget.budgetType
      },
      period: args.period,
      varianceThreshold: threshold,
      summary: {
        totalVariances: varianceAnalysis.length,
        unfavorableVariances: varianceAnalysis.filter(item => item.isUnfavorable).length,
        favorableVariances: varianceAnalysis.filter(item => !item.isUnfavorable).length,
        totalUnfavorableVariance,
        totalFavorableVariance,
        netVariance: totalFavorableVariance - totalUnfavorableVariance,
        highSeverityCount,
        mediumSeverityCount
      },
      varianceAnalysis,
      currency: "PHP",
      generatedAt: new Date().toISOString()
    };
  }
});
