import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

// ============================================================================
// EXPENSE MANAGEMENT SYSTEM
// Philippine GAAP Compliant • BIR VAT Compliant • Input VAT & Withholding Tax
// ============================================================================

// Record Purchase Transaction with Input VAT and Withholding Tax
export const recordPurchaseTransaction = mutation({
  args: {
    supplierId: v.id("suppliers"),
    invoiceNumber: v.string(),
    invoiceDate: v.string(),
    dueDate: v.string(),
    lineItems: v.array(v.object({
      accountCode: v.string(),
      description: v.string(),
      amount: v.number(),
      vatType: v.union(v.literal("Vatable"), v.literal("VAT Exempt"), v.literal("Zero Rated"))
    })),
    vatableAmount: v.number(),
    vatExemptAmount: v.number(),
    zeroRatedAmount: v.number(),
    inputVAT: v.number(),
    withholdingTax: v.number(),
    withholdingTaxRate: v.number(),
    totalAmount: v.number(),
    paymentTerms: v.string(),
    departmentId: v.optional(v.string()),
    costCenterId: v.optional(v.string())
  },
  handler: async (ctx, args) => {
    // Get required accounts
    const accounts = await ctx.db.query("accounts").collect();
    const apAccount = accounts.find(acc => acc.code === "200");
    const inputVATAccount = accounts.find(acc => acc.code === "118");
    const withholdingTaxAccount = accounts.find(acc => acc.code === "119");

    if (!apAccount || !inputVATAccount) {
      throw new Error("Required accounts not found. Please ensure Chart of Accounts includes: 200-Accounts Payable, 118-Input VAT");
    }

    // Get current fiscal period
    const currentPeriod = await ctx.db.query("fiscalPeriods")
      .filter(q => q.and(
        q.lte(q.field("startDate"), args.invoiceDate),
        q.gte(q.field("endDate"), args.invoiceDate),
        q.eq(q.field("status"), "Open")
      ))
      .first();

    if (!currentPeriod) {
      throw new Error("No open fiscal period found for the transaction date");
    }

    // Create vendor invoice
    const vendorInvoiceId = await ctx.db.insert("vendorInvoices", {
      supplierId: args.supplierId,
      invoiceNumber: args.invoiceNumber,
      invoiceDate: args.invoiceDate,
      dueDate: args.dueDate,
      totalAmount: args.totalAmount,
      paidAmount: 0,
      status: "Approved",
      description: `Purchase Invoice ${args.invoiceNumber}`
    });

    // Generate journal entry batch
    const batchId = `PI-${args.invoiceNumber}`;
    const netPayable = args.totalAmount - args.withholdingTax;

    // Journal Entries for Purchase Transaction
    let entryCounter = 1;

    // 1. Debit: Expense/Asset Accounts (per line items)
    for (const item of args.lineItems) {
      const expenseAccount = accounts.find(acc => acc.code === item.accountCode);
      if (!expenseAccount) {
        throw new Error(`Account with code ${item.accountCode} not found`);
      }

      await ctx.db.insert("journalEntries", {
        entryNumber: `${batchId}-${String(entryCounter++).padStart(3, '0')}`,
        date: args.invoiceDate,
        accountId: expenseAccount._id,
        description: `${item.description} - ${args.invoiceNumber}`,
        debit: item.amount,
        credit: 0,
        reference: args.invoiceNumber,
        transactionType: "Purchase",
        departmentId: args.departmentId || "",
        costCenterId: args.costCenterId || "",
        fiscalPeriodId: currentPeriod._id,
        isPosted: true,
        postedBy: "System",
        postedDate: args.invoiceDate,
        batchId: batchId
      });

      // Update account balance
      await updateAccountBalance(ctx, expenseAccount._id, currentPeriod._id, item.amount, 0);
    }

    // 2. Debit: Input VAT (if applicable)
    if (args.inputVAT > 0) {
      await ctx.db.insert("journalEntries", {
        entryNumber: `${batchId}-${String(entryCounter++).padStart(3, '0')}`,
        date: args.invoiceDate,
        accountId: inputVATAccount._id,
        description: `Input VAT - ${args.invoiceNumber}`,
        debit: args.inputVAT,
        credit: 0,
        reference: args.invoiceNumber,
        transactionType: "Purchase",
        departmentId: args.departmentId || "",
        costCenterId: args.costCenterId || "",
        fiscalPeriodId: currentPeriod._id,
        isPosted: true,
        postedBy: "System",
        postedDate: args.invoiceDate,
        batchId: batchId
      });

      await updateAccountBalance(ctx, inputVATAccount._id, currentPeriod._id, args.inputVAT, 0);
    }

    // 3. Debit: Creditable Withholding Tax (if applicable)
    if (args.withholdingTax > 0 && withholdingTaxAccount) {
      await ctx.db.insert("journalEntries", {
        entryNumber: `${batchId}-${String(entryCounter++).padStart(3, '0')}`,
        date: args.invoiceDate,
        accountId: withholdingTaxAccount._id,
        description: `Withholding Tax (${(args.withholdingTaxRate * 100).toFixed(1)}%) - ${args.invoiceNumber}`,
        debit: args.withholdingTax,
        credit: 0,
        reference: args.invoiceNumber,
        transactionType: "Purchase",
        departmentId: args.departmentId || "",
        costCenterId: args.costCenterId || "",
        fiscalPeriodId: currentPeriod._id,
        isPosted: true,
        postedBy: "System",
        postedDate: args.invoiceDate,
        batchId: batchId
      });

      await updateAccountBalance(ctx, withholdingTaxAccount._id, currentPeriod._id, args.withholdingTax, 0);
    }

    // 4. Credit: Accounts Payable
    await ctx.db.insert("journalEntries", {
      entryNumber: `${batchId}-${String(entryCounter++).padStart(3, '0')}`,
      date: args.invoiceDate,
      accountId: apAccount._id,
      description: `Purchase - ${args.invoiceNumber}`,
      debit: 0,
      credit: netPayable,
      reference: args.invoiceNumber,
      transactionType: "Purchase",
      departmentId: args.departmentId || "",
      costCenterId: args.costCenterId || "",
      fiscalPeriodId: currentPeriod._id,
      isPosted: true,
      postedBy: "System",
      postedDate: args.invoiceDate,
      batchId: batchId
    });

    await updateAccountBalance(ctx, apAccount._id, currentPeriod._id, 0, netPayable);

    // Record VAT transaction for BIR compliance
    await ctx.db.insert("vatTransactions", {
      transactionDate: args.invoiceDate,
      transactionType: "Purchase",
      documentType: "Purchase Invoice",
      documentNumber: args.invoiceNumber,
      supplierId: args.supplierId,
      vatableAmount: args.vatableAmount,
      vatExemptAmount: args.vatExemptAmount,
      zeroRatedAmount: args.zeroRatedAmount,
      vatAmount: args.inputVAT,
      vatRate: 0.12,
      withholdingTaxAmount: args.withholdingTax,
      withholdingTaxRate: args.withholdingTaxRate,
      netAmount: args.vatableAmount + args.vatExemptAmount + args.zeroRatedAmount,
      totalAmount: args.totalAmount,
      fiscalPeriodId: currentPeriod._id
    });

    return {
      success: true,
      vendorInvoiceId,
      message: `✅ Purchase transaction recorded successfully\n\n📋 BIR Compliance:\n• Expense Amount: ₱${(args.vatableAmount + args.vatExemptAmount + args.zeroRatedAmount).toLocaleString()}\n• Input VAT (12%): ₱${args.inputVAT.toLocaleString()}\n• Withholding Tax (${(args.withholdingTaxRate * 100).toFixed(1)}%): ₱${args.withholdingTax.toLocaleString()}\n• Net Payable: ₱${netPayable.toLocaleString()}\n• Batch ID: ${batchId}`,
      totalAmount: args.totalAmount,
      inputVAT: args.inputVAT,
      withholdingTax: args.withholdingTax,
      netPayable: netPayable,
      batchId,
      birCompliant: true
    };
  }
});

// Record Supplier Payment
export const recordSupplierPayment = mutation({
  args: {
    vendorInvoiceId: v.id("vendorInvoices"),
    paymentDate: v.string(),
    paymentAmount: v.number(),
    paymentMethod: v.union(v.literal("Cash"), v.literal("Check"), v.literal("Bank Transfer")),
    referenceNumber: v.string(),
    bankAccountCode: v.optional(v.string()),
    departmentId: v.optional(v.string()),
    costCenterId: v.optional(v.string())
  },
  handler: async (ctx, args) => {
    const vendorInvoice = await ctx.db.get(args.vendorInvoiceId);
    if (!vendorInvoice) {
      throw new Error("Vendor invoice not found");
    }

    const remainingBalance = vendorInvoice.totalAmount - vendorInvoice.paidAmount;
    if (args.paymentAmount > remainingBalance) {
      throw new Error(`Payment amount (₱${args.paymentAmount.toLocaleString()}) exceeds remaining balance (₱${remainingBalance.toLocaleString()})`);
    }

    // Get required accounts
    const accounts = await ctx.db.query("accounts").collect();
    const cashAccount = accounts.find(acc => acc.code === (args.bankAccountCode || "111"));
    const apAccount = accounts.find(acc => acc.code === "200");

    if (!cashAccount || !apAccount) {
      throw new Error("Required accounts not found");
    }

    // Get current fiscal period
    const currentPeriod = await ctx.db.query("fiscalPeriods")
      .filter(q => q.and(
        q.lte(q.field("startDate"), args.paymentDate),
        q.gte(q.field("endDate"), args.paymentDate),
        q.eq(q.field("status"), "Open")
      ))
      .first();

    if (!currentPeriod) {
      throw new Error("No open fiscal period found for the payment date");
    }

    // Generate journal entry batch
    const batchId = `AP-${args.referenceNumber}`;

    // Journal Entries for Payment
    // 1. Debit: Accounts Payable
    await ctx.db.insert("journalEntries", {
      entryNumber: `${batchId}-001`,
      date: args.paymentDate,
      accountId: apAccount._id,
      description: `Payment - Invoice ${vendorInvoice.invoiceNumber}`,
      debit: args.paymentAmount,
      credit: 0,
      reference: args.referenceNumber,
      transactionType: "Payment",
      departmentId: args.departmentId || "",
      costCenterId: args.costCenterId || "",
      fiscalPeriodId: currentPeriod._id,
      isPosted: true,
      postedBy: "System",
      postedDate: args.paymentDate,
      batchId: batchId
    });

    // 2. Credit: Cash/Bank
    await ctx.db.insert("journalEntries", {
      entryNumber: `${batchId}-002`,
      date: args.paymentDate,
      accountId: cashAccount._id,
      description: `Payment to supplier - Invoice ${vendorInvoice.invoiceNumber}`,
      debit: 0,
      credit: args.paymentAmount,
      reference: args.referenceNumber,
      transactionType: "Payment",
      departmentId: args.departmentId || "",
      costCenterId: args.costCenterId || "",
      fiscalPeriodId: currentPeriod._id,
      isPosted: true,
      postedBy: "System",
      postedDate: args.paymentDate,
      batchId: batchId
    });

    // Update account balances
    await updateAccountBalance(ctx, apAccount._id, currentPeriod._id, args.paymentAmount, 0);
    await updateAccountBalance(ctx, cashAccount._id, currentPeriod._id, 0, args.paymentAmount);

    // Update vendor invoice payment status
    const newPaidAmount = vendorInvoice.paidAmount + args.paymentAmount;
    const newStatus = newPaidAmount >= vendorInvoice.totalAmount ? "Paid" : "Approved";

    await ctx.db.patch(args.vendorInvoiceId, {
      paidAmount: newPaidAmount,
      status: newStatus
    });

    return {
      success: true,
      message: `✅ Supplier payment recorded successfully\n\n💰 Payment Details:\n• Amount Paid: ₱${args.paymentAmount.toLocaleString()}\n• Total Paid: ₱${newPaidAmount.toLocaleString()}\n• Remaining Balance: ₱${(vendorInvoice.totalAmount - newPaidAmount).toLocaleString()}\n• Batch ID: ${batchId}`,
      paidAmount: args.paymentAmount,
      totalPaid: newPaidAmount,
      remainingBalance: vendorInvoice.totalAmount - newPaidAmount,
      status: newStatus,
      batchId
    };
  }
});

// Helper function to update account balances (shared with other modules)
async function updateAccountBalance(
  ctx: any, 
  accountId: string, 
  fiscalPeriodId: string, 
  debitAmount: number, 
  creditAmount: number
) {
  const existingBalance = await ctx.db.query("accountBalances")
    .filter(q => q.and(
      q.eq(q.field("accountId"), accountId),
      q.eq(q.field("fiscalPeriodId"), fiscalPeriodId)
    ))
    .first();

  if (existingBalance) {
    await ctx.db.patch(existingBalance._id, {
      totalDebits: existingBalance.totalDebits + debitAmount,
      totalCredits: existingBalance.totalCredits + creditAmount,
      closingBalance: existingBalance.openingBalance + 
        (existingBalance.totalDebits + debitAmount) - 
        (existingBalance.totalCredits + creditAmount),
      lastUpdated: new Date().toISOString()
    });
  } else {
    await ctx.db.insert("accountBalances", {
      accountId: accountId,
      fiscalPeriodId: fiscalPeriodId,
      openingBalance: 0,
      totalDebits: debitAmount,
      totalCredits: creditAmount,
      closingBalance: debitAmount - creditAmount,
      lastUpdated: new Date().toISOString()
    });
  }
}
