import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  LineChart,
  Line,
  RadialBar<PERSON>hart,
  RadialBar,
  ResponsiveContainer
} from 'recharts';

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D', '#FFC658', '#FF7C7C'];

interface ManufacturingChartsProps {
  data: any;
}

export const ProductionStatusChart: React.FC<{ data: any }> = ({ data }) => {
  const productionData = [
    { name: 'Active', value: data?.production?.activeOrders || 0, color: '#3B82F6' },
    { name: 'Completed', value: data?.production?.completedOrders || 0, color: '#10B981' },
    { name: 'Total', value: data?.production?.totalOrders || 0, color: '#6B7280' }
  ];

  const isEmpty = !data?.production || data.production.totalOrders === 0;

  return (
    <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
      <h3 className="text-lg font-semibold text-gray-800 mb-4">Production Orders Status</h3>
      {isEmpty && (
        <div className="text-center py-8 text-gray-500">
          <p>No production data available</p>
          <p className="text-sm">Load sample data to see production order status</p>
        </div>
      )}
      <ResponsiveContainer width="100%" height={300}>
        <PieChart>
          <Pie
            data={productionData}
            cx="50%"
            cy="50%"
            labelLine={false}
            label={({ name, value, percent }) => `${name}: ${value} (${(percent * 100).toFixed(0)}%)`}
            outerRadius={80}
            fill="#8884d8"
            dataKey="value"
          >
            {productionData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color} />
            ))}
          </Pie>
          <Tooltip />
        </PieChart>
      </ResponsiveContainer>
    </div>
  );
};

export const InventoryCategoryChart: React.FC<{ data: any }> = ({ data }) => {
  // Create realistic inventory distribution even with empty data
  const totalItems = data?.inventory?.totalItems || 0;
  const inventoryData = [
    { name: 'Raw Materials', value: totalItems > 0 ? Math.floor(totalItems * 0.4) : 0, color: '#8B5CF6' },
    { name: 'Work in Progress', value: totalItems > 0 ? Math.floor(totalItems * 0.2) : 0, color: '#F59E0B' },
    { name: 'Finished Goods', value: totalItems > 0 ? Math.floor(totalItems * 0.3) : 0, color: '#10B981' },
    { name: 'Spare Parts', value: totalItems > 0 ? Math.floor(totalItems * 0.1) : 0, color: '#EF4444' }
  ];

  const isEmpty = !data?.inventory || data.inventory.totalItems === 0;

  return (
    <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
      <h3 className="text-lg font-semibold text-gray-800 mb-4">Inventory by Category</h3>
      {isEmpty && (
        <div className="text-center py-8 text-gray-500">
          <p>No inventory data available</p>
          <p className="text-sm">Load sample data to see inventory category breakdown</p>
        </div>
      )}
      <ResponsiveContainer width="100%" height={300}>
        <BarChart data={inventoryData} layout="horizontal">
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis type="number" />
          <YAxis dataKey="name" type="category" width={100} />
          <Tooltip />
          <Bar dataKey="value" fill="#8884D8">
            {inventoryData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color} />
            ))}
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

export const EfficiencyGaugeChart: React.FC<{ data: any }> = ({ data }) => {
  const efficiency = data?.production?.efficiency || 0;
  const gaugeData = [
    { name: 'Efficiency', value: efficiency, fill: efficiency >= 80 ? '#10B981' : efficiency >= 60 ? '#F59E0B' : '#EF4444' }
  ];

  const isEmpty = !data?.production;

  return (
    <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
      <h3 className="text-lg font-semibold text-gray-800 mb-4">Production Efficiency</h3>
      {isEmpty && (
        <div className="text-center py-8 text-gray-500">
          <p>No efficiency data available</p>
          <p className="text-sm">Load sample data to see production efficiency gauge</p>
        </div>
      )}
      <ResponsiveContainer width="100%" height={300}>
        <RadialBarChart cx="50%" cy="50%" innerRadius="60%" outerRadius="90%" data={gaugeData}>
          <RadialBar
            minAngle={15}
            label={{ position: 'insideStart', fill: '#fff' }}
            background
            clockWise
            dataKey="value"
          />
          <Tooltip formatter={(value) => [`${value}%`, 'Efficiency']} />
        </RadialBarChart>
      </ResponsiveContainer>
      <div className="text-center mt-4">
        <span className="text-2xl font-bold text-gray-800">{efficiency.toFixed(1)}%</span>
        <p className="text-gray-600">Overall Efficiency</p>
      </div>
    </div>
  );
};

export const SupplierPerformanceChart: React.FC<{ data: any }> = ({ data }) => {
  // Mock supplier performance data - in real app this would come from database
  const supplierData = [
    { name: 'Steel Dynamics', quality: 95, delivery: 88, cost: 92 },
    { name: 'Alcoa Corp', quality: 92, delivery: 95, cost: 85 },
    { name: 'Bosch Auto', quality: 98, delivery: 90, cost: 88 },
    { name: 'Continental', quality: 90, delivery: 85, cost: 90 }
  ];

  const isEmpty = !data?.procurement || data.procurement.totalSuppliers === 0;

  return (
    <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
      <h3 className="text-lg font-semibold text-gray-800 mb-4">Supplier Performance Metrics</h3>
      {isEmpty && (
        <div className="text-center py-8 text-gray-500">
          <p>No supplier data available</p>
          <p className="text-sm">Load sample data to see supplier performance metrics</p>
        </div>
      )}
      <ResponsiveContainer width="100%" height={300}>
        <BarChart data={supplierData}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="name" angle={-45} textAnchor="end" height={80} />
          <YAxis />
          <Tooltip />
          <Legend />
          <Bar dataKey="quality" fill="#10B981" name="Quality %" />
          <Bar dataKey="delivery" fill="#3B82F6" name="Delivery %" />
          <Bar dataKey="cost" fill="#F59E0B" name="Cost Efficiency %" />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

export const MonthlyTrendsChart: React.FC<{ data: any }> = ({ data }) => {
  // Mock monthly trend data - in real app this would come from database
  const monthlyData = [
    { month: 'Jan', revenue: 450000, production: 85, inventory: 1200000 },
    { month: 'Feb', revenue: 520000, production: 88, inventory: 1150000 },
    { month: 'Mar', revenue: 480000, production: 82, inventory: 1300000 },
    { month: 'Apr', revenue: 610000, production: 90, inventory: 1100000 },
    { month: 'May', revenue: 580000, production: 87, inventory: 1250000 },
    { month: 'Jun', revenue: 650000, production: 92, inventory: 1050000 }
  ];

  const isEmpty = !data || data.revenue === 0;

  return (
    <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
      <h3 className="text-lg font-semibold text-gray-800 mb-4">Monthly Performance Trends</h3>
      {isEmpty && (
        <div className="text-center py-8 text-gray-500">
          <p>No trend data available</p>
          <p className="text-sm">Load sample data to see monthly performance trends</p>
        </div>
      )}
      <ResponsiveContainer width="100%" height={300}>
        <LineChart data={monthlyData}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="month" />
          <YAxis yAxisId="left" />
          <YAxis yAxisId="right" orientation="right" />
          <Tooltip 
            formatter={(value, name) => {
              if (name === 'Revenue') return [`$${Number(value).toLocaleString()}`, name];
              if (name === 'Inventory Value') return [`$${Number(value).toLocaleString()}`, name];
              return [`${value}%`, name];
            }}
          />
          <Legend />
          <Line yAxisId="left" type="monotone" dataKey="revenue" stroke="#10B981" strokeWidth={3} name="Revenue" />
          <Line yAxisId="right" type="monotone" dataKey="production" stroke="#3B82F6" strokeWidth={3} name="Production Efficiency %" />
          <Line yAxisId="left" type="monotone" dataKey="inventory" stroke="#F59E0B" strokeWidth={3} name="Inventory Value" />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};

export const QualityMetricsChart: React.FC<{ data: any }> = ({ data }) => {
  const qualityData = [
    { name: 'Pass Rate', value: 94.5, target: 95, color: '#10B981' },
    { name: 'First Pass Yield', value: 87.2, target: 90, color: '#3B82F6' },
    { name: 'Customer Returns', value: 2.1, target: 1.5, color: '#EF4444' },
    { name: 'Defect Rate', value: 1.8, target: 2.0, color: '#F59E0B' }
  ];

  const isEmpty = !data;

  return (
    <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
      <h3 className="text-lg font-semibold text-gray-800 mb-4">Quality Control Metrics</h3>
      {isEmpty && (
        <div className="text-center py-8 text-gray-500">
          <p>No quality data available</p>
          <p className="text-sm">Load sample data to see quality control metrics</p>
        </div>
      )}
      <ResponsiveContainer width="100%" height={300}>
        <BarChart data={qualityData}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="name" angle={-45} textAnchor="end" height={80} />
          <YAxis />
          <Tooltip formatter={(value) => [`${value}%`, 'Value']} />
          <Legend />
          <Bar dataKey="value" name="Actual">
            {qualityData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color} />
            ))}
          </Bar>
          <Bar dataKey="target" fill="#E5E7EB" name="Target" />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

export const CostBreakdownChart: React.FC<{ data: any }> = ({ data }) => {
  const costData = [
    { name: 'Raw Materials', value: 45, color: '#8B5CF6' },
    { name: 'Direct Labor', value: 25, color: '#10B981' },
    { name: 'Manufacturing Overhead', value: 20, color: '#F59E0B' },
    { name: 'Administrative', value: 10, color: '#EF4444' }
  ];

  const isEmpty = !data || data.expenses === 0;

  return (
    <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
      <h3 className="text-lg font-semibold text-gray-800 mb-4">Manufacturing Cost Breakdown</h3>
      {isEmpty && (
        <div className="text-center py-8 text-gray-500">
          <p>No cost data available</p>
          <p className="text-sm">Load sample data to see manufacturing cost breakdown</p>
        </div>
      )}
      <ResponsiveContainer width="100%" height={300}>
        <PieChart>
          <Pie
            data={costData}
            cx="50%"
            cy="50%"
            labelLine={false}
            label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
            outerRadius={80}
            fill="#8884d8"
            dataKey="value"
          >
            {costData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color} />
            ))}
          </Pie>
          <Tooltip formatter={(value) => [`${value}%`, 'Percentage']} />
        </PieChart>
      </ResponsiveContainer>
    </div>
  );
};

const ManufacturingCharts: React.FC<ManufacturingChartsProps> = ({ data }) => {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <ProductionStatusChart data={data} />
        <InventoryCategoryChart data={data} />
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <EfficiencyGaugeChart data={data} />
        <CostBreakdownChart data={data} />
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <SupplierPerformanceChart data={data} />
        <QualityMetricsChart data={data} />
      </div>
      
      <MonthlyTrendsChart data={data} />
    </div>
  );
};

export default ManufacturingCharts;
