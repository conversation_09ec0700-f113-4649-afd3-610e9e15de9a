import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  accounts: defineTable({
    name: v.string(),
    type: v.union(
      v.literal("Asset"),
      v.literal("Liability"),
      v.literal("Equity"),
      v.literal("Revenue"),
      v.literal("Expense")
    ),
    code: v.string(),
    subType: v.optional(v.union(
      v.literal("Current Asset"), v.literal("Fixed Asset"), v.literal("Inventory"),
      v.literal("Current Liability"), v.literal("Long-term Liability"),
      v.literal("Operating Revenue"), v.literal("Non-operating Revenue"),
      v.literal("Cost of Goods Sold"), v.literal("Operating Expense"), v.literal("Administrative Expense")
    )),
    parentAccountId: v.optional(v.id("accounts")),
    isActive: v.optional(v.boolean()),
    currentBalance: v.optional(v.number()),
    openingBalance: v.optional(v.number()),
    description: v.optional(v.string()),
    normalBalance: v.union(v.literal("Debit"), v.literal("Credit")),
    isControlAccount: v.optional(v.boolean()),
    departmentId: v.optional(v.string()),
    costCenterId: v.optional(v.string()),
  }),

  // Fiscal Periods for Philippine GAAP compliance
  fiscalPeriods: defineTable({
    periodName: v.string(), // e.g., "January 2024", "Q1 2024", "FY 2024"
    periodType: v.union(v.literal("Monthly"), v.literal("Quarterly"), v.literal("Yearly")),
    startDate: v.string(),
    endDate: v.string(),
    status: v.union(v.literal("Open"), v.literal("Closed"), v.literal("Locked")),
    fiscalYear: v.number(),
    quarter: v.optional(v.number()),
    month: v.optional(v.number()),
    closedBy: v.optional(v.string()),
    closedDate: v.optional(v.string()),
    notes: v.optional(v.string()),
  }),

  // Account Balances per Period - Enhanced for Balance Control
  accountBalances: defineTable({
    accountId: v.id("accounts"),
    fiscalPeriodId: v.id("fiscalPeriods"),
    openingBalance: v.number(),
    totalDebits: v.number(),
    totalCredits: v.number(),
    closingBalance: v.number(),
    adjustments: v.optional(v.number()),
    lastUpdated: v.string(),
    isLocked: v.optional(v.boolean()),
    lockedBy: v.optional(v.string()),
    lockedDate: v.optional(v.string()),
  }),

  // Starting/Ending Balance Control Table
  balanceControl: defineTable({
    accountId: v.id("accounts"),
    fiscalPeriodId: v.id("fiscalPeriods"),
    periodStartDate: v.string(),
    periodEndDate: v.string(),
    startingBalance: v.number(),
    endingBalance: v.number(),
    minimumBalance: v.optional(v.number()), // Prevent negative balances
    maximumBalance: v.optional(v.number()), // Set balance limits
    balanceType: v.union(v.literal("Debit"), v.literal("Credit")),
    isBalanceValid: v.boolean(),
    validationErrors: v.optional(v.array(v.string())),
    lastValidated: v.string(),
    createdBy: v.string(),
    createdDate: v.string(),
  }),

  // Balance Validation Rules
  balanceValidationRules: defineTable({
    accountType: v.union(v.literal("Asset"), v.literal("Liability"), v.literal("Equity"), v.literal("Revenue"), v.literal("Expense")),
    ruleName: v.string(),
    ruleDescription: v.string(),
    allowNegativeBalance: v.boolean(),
    minimumBalanceThreshold: v.optional(v.number()),
    maximumBalanceThreshold: v.optional(v.number()),
    warningThreshold: v.optional(v.number()),
    isActive: v.boolean(),
    createdDate: v.string(),
    lastModified: v.string(),
  }),

  // Enhanced Journal Entries
  journalEntries: defineTable({
    entryNumber: v.string(), // Sequential journal entry number
    date: v.string(),
    accountId: v.id("accounts"),
    description: v.string(),
    debit: v.number(),
    credit: v.number(),
    reference: v.optional(v.string()),
    transactionType: v.optional(v.union(
      v.literal("Sales"), v.literal("Purchase"), v.literal("Production"),
      v.literal("Payroll"), v.literal("Depreciation"), v.literal("Adjustment"),
      v.literal("Revenue Recognition"), v.literal("Payment"), v.literal("Opening Balance"),
      v.literal("Closing Entry"), v.literal("Accrual"), v.literal("Reversal")
    )),
    departmentId: v.optional(v.string()),
    costCenterId: v.optional(v.string()),
    projectId: v.optional(v.string()),
    fiscalPeriodId: v.optional(v.id("fiscalPeriods")),
    isPosted: v.optional(v.boolean()),
    postedBy: v.optional(v.string()),
    postedDate: v.optional(v.string()),
    isReversed: v.optional(v.boolean()),
    reversalEntryId: v.optional(v.id("journalEntries")),
    batchId: v.optional(v.string()), // Group related entries
  }),

  // Enhanced VAT Transactions for BIR Compliance
  vatTransactions: defineTable({
    transactionDate: v.string(),
    transactionType: v.union(v.literal("Sales"), v.literal("Purchase")),
    documentType: v.union(
      v.literal("Sales Invoice"), v.literal("Official Receipt"),
      v.literal("Purchase Invoice"), v.literal("Debit Memo"), v.literal("Credit Memo")
    ),
    documentNumber: v.string(),
    customerId: v.optional(v.id("customers")),
    supplierId: v.optional(v.id("suppliers")),
    vatableAmount: v.number(),
    vatExemptAmount: v.number(),
    zeroRatedAmount: v.number(),
    vatAmount: v.number(),
    vatRate: v.number(), // 0.12 for 12% Philippine VAT
    withholdingTaxAmount: v.number(),
    withholdingTaxRate: v.number(),
    netAmount: v.number(),
    totalAmount: v.number(),
    tinNumber: v.optional(v.string()),
    businessStyle: v.optional(v.string()),
    address: v.optional(v.string()),
    fiscalPeriodId: v.optional(v.id("fiscalPeriods")),
  }),

  // BIR Forms Tracking
  birForms: defineTable({
    formType: v.union(
      v.literal("2550M"), v.literal("2550Q"), v.literal("1601E"),
      v.literal("1601Q"), v.literal("2307"), v.literal("1702Q"), v.literal("1702A")
    ),
    periodCovered: v.string(),
    filingDate: v.string(),
    dueDate: v.string(),
    status: v.union(v.literal("Draft"), v.literal("Filed"), v.literal("Paid")),
    totalTaxDue: v.number(),
    totalTaxPaid: v.number(),
    penalties: v.optional(v.number()),
    interest: v.optional(v.number()),
    referenceNumber: v.optional(v.string()),
    filedBy: v.optional(v.string()),
    notes: v.optional(v.string()),
  }),

  // Budget Management
  budgets: defineTable({
    budgetName: v.string(),
    fiscalYear: v.number(),
    budgetType: v.union(v.literal("Operating"), v.literal("Capital"), v.literal("Cash Flow")),
    status: v.union(v.literal("Draft"), v.literal("Approved"), v.literal("Active"), v.literal("Closed")),
    totalBudgetAmount: v.number(),
    approvedBy: v.optional(v.string()),
    approvedDate: v.optional(v.string()),
    notes: v.optional(v.string()),
  }),

  budgetLineItems: defineTable({
    budgetId: v.id("budgets"),
    accountId: v.id("accounts"),
    departmentId: v.optional(v.string()),
    costCenterId: v.optional(v.string()),
    budgetedAmount: v.number(),
    actualAmount: v.optional(v.number()),
    variance: v.optional(v.number()),
    variancePercent: v.optional(v.number()),
    period: v.string(), // Monthly breakdown
    notes: v.optional(v.string()),
  }),

  // Fixed Assets for PAS 16 Compliance
  fixedAssets: defineTable({
    assetNumber: v.string(),
    assetName: v.string(),
    assetCategory: v.union(
      v.literal("Building"), v.literal("Machinery"), v.literal("Equipment"),
      v.literal("Furniture"), v.literal("Vehicles"), v.literal("Computer Equipment"),
      v.literal("Leasehold Improvements"), v.literal("Other")
    ),
    acquisitionDate: v.string(),
    acquisitionCost: v.number(),
    accumulatedDepreciation: v.number(),
    bookValue: v.number(),
    depreciationMethod: v.union(
      v.literal("Straight Line"), v.literal("Declining Balance"),
      v.literal("Sum of Years Digits"), v.literal("Units of Production")
    ),
    usefulLife: v.number(), // in years
    salvageValue: v.number(),
    monthlyDepreciation: v.number(),
    location: v.optional(v.string()),
    departmentId: v.optional(v.string()),
    costCenterId: v.optional(v.string()),
    serialNumber: v.optional(v.string()),
    supplier: v.optional(v.string()),
    warrantyExpiry: v.optional(v.string()),
    status: v.union(v.literal("Active"), v.literal("Disposed"), v.literal("Impaired")),
    disposalDate: v.optional(v.string()),
    disposalAmount: v.optional(v.number()),
    gainLossOnDisposal: v.optional(v.number()),
    lastDepreciationDate: v.optional(v.string()),
    assetAccountId: v.optional(v.id("accounts")),
    depreciationAccountId: v.optional(v.id("accounts")),
  }),

  // Depreciation Schedule
  depreciationSchedule: defineTable({
    assetId: v.id("fixedAssets"),
    periodDate: v.string(), // YYYY-MM format
    openingBookValue: v.number(),
    depreciationExpense: v.number(),
    accumulatedDepreciation: v.number(),
    closingBookValue: v.number(),
    isPosted: v.optional(v.boolean()),
    journalEntryId: v.optional(v.id("journalEntries")),
    fiscalPeriodId: v.optional(v.id("fiscalPeriods")),
  }),

  invoices: defineTable({
    customerId: v.id("customers"),
    invoiceNumber: v.string(),
    dateIssued: v.string(), // Use ISO string for date
    dueDate: v.string(),    // Use ISO string for date
    totalAmount: v.number(),
    status: v.union(
      v.literal("Pending"),
      v.literal("Paid"),
      v.literal("Overdue")
    ),
  }),

  payments: defineTable({
    invoiceId: v.id("invoices"),
    date: v.string(), // Use ISO string for date
    amount: v.number(),
    method: v.union(
      v.literal("Cash"),
      v.literal("Credit Card"),
      v.literal("Bank Transfer"),
      v.literal("Check")
    ),
    reference: v.optional(v.string()),
  }),

  customers: defineTable({
    name: v.string(),
    contactEmail: v.string(),
    phone: v.optional(v.string()),
    address: v.optional(v.string()),
    customerType: v.optional(v.union(v.literal("OEM"), v.literal("Aftermarket"), v.literal("Distributor"))),
    creditLimit: v.optional(v.number()),
    paymentTerms: v.optional(v.string()),
  }),

  // Enhanced Financial Tables for Manufacturing
  costCenters: defineTable({
    code: v.string(),
    name: v.string(),
    type: v.union(v.literal("Production"), v.literal("Service"), v.literal("Administrative")),
    managerId: v.optional(v.id("employees")),
    budgetAmount: v.optional(v.number()),
    isActive: v.boolean(),
  }),



  // Manufacturing Cost Tracking
  workOrderCosts: defineTable({
    workOrderId: v.id("workOrders"),
    costType: v.union(v.literal("Material"), v.literal("Labor"), v.literal("Overhead")),
    accountId: v.id("accounts"),
    amount: v.number(),
    quantity: v.optional(v.number()),
    unitCost: v.optional(v.number()),
    date: v.string(),
    description: v.string(),
  }),



  // Accounts Payable
  vendorInvoices: defineTable({
    supplierId: v.id("suppliers"),
    invoiceNumber: v.string(),
    invoiceDate: v.string(),
    dueDate: v.string(),
    totalAmount: v.number(),
    paidAmount: v.number(),
    status: v.union(v.literal("Pending"), v.literal("Approved"), v.literal("Paid"), v.literal("Overdue")),
    purchaseOrderId: v.optional(v.string()),
    description: v.string(),
  }),

  // Accounts Receivable - Enhanced for PFRS 15 Compliance
  customerInvoices: defineTable({
    customerId: v.id("customers"),
    invoiceNumber: v.string(),
    invoiceDate: v.string(),
    dueDate: v.string(),
    totalAmount: v.number(),
    paidAmount: v.number(),
    status: v.union(v.literal("Draft"), v.literal("Sent"), v.literal("Paid"), v.literal("Overdue"), v.literal("Cancelled")),
    salesOrderId: v.optional(v.string()),
    taxAmount: v.number(),
    discountAmount: v.number(),
    // PFRS 15 Revenue Recognition fields
    revenueRecognitionStatus: v.optional(v.union(
      v.literal("Not Recognized"),
      v.literal("Partially Recognized"),
      v.literal("Fully Recognized")
    )),
    revenueRecognizedAmount: v.optional(v.number()),
    performanceObligationsFulfilled: v.optional(v.boolean()),
    deliveryDate: v.optional(v.string()),
    controlTransferredDate: v.optional(v.string()),
    // Philippine VAT Compliance
    vatableAmount: v.optional(v.number()),
    vatExemptAmount: v.optional(v.number()),
    zeroRatedAmount: v.optional(v.number()),
    inputVatAmount: v.optional(v.number()),
    outputVatAmount: v.optional(v.number()),
    withholdingTaxAmount: v.optional(v.number()),
  }),

  // PFRS 15 Performance Obligations
  performanceObligations: defineTable({
    invoiceId: v.id("customerInvoices"),
    description: v.string(),
    allocatedAmount: v.number(),
    status: v.union(v.literal("Pending"), v.literal("In Progress"), v.literal("Satisfied")),
    satisfactionDate: v.optional(v.string()),
    satisfactionMethod: v.optional(v.union(v.literal("Point in Time"), v.literal("Over Time"))),
    percentageComplete: v.optional(v.number()),
  }),

  // Contract Liabilities (Deferred Revenue) - PFRS 15
  contractLiabilities: defineTable({
    customerId: v.id("customers"),
    contractNumber: v.string(),
    description: v.string(),
    totalContractValue: v.number(),
    advancePaymentReceived: v.number(),
    revenueRecognized: v.number(),
    remainingLiability: v.number(),
    contractDate: v.string(),
    expectedCompletionDate: v.string(),
    status: v.union(v.literal("Active"), v.literal("Completed"), v.literal("Cancelled")),
  }),

  // Manufacturing Tables - Enhanced for PFRS/PAS 2 Inventory Compliance
  inventory: defineTable({
    partNumber: v.string(),
    partName: v.string(),
    category: v.union(v.literal("Raw Material"), v.literal("Work in Progress"), v.literal("Finished Goods"), v.literal("Spare Parts")),
    currentStock: v.number(),
    minimumStock: v.number(),
    maximumStock: v.number(),
    unitCost: v.number(), // Weighted average or FIFO cost
    location: v.string(),
    supplierId: v.optional(v.id("suppliers")),
    // PFRS/PAS 2 Inventory Costing
    costingMethod: v.union(v.literal("FIFO"), v.literal("Weighted Average")),
    lastCostUpdate: v.optional(v.string()),
    standardCost: v.optional(v.number()),
    averageCost: v.optional(v.number()),
  }),

  // Inventory Cost Layers for FIFO/Weighted Average
  inventoryCostLayers: defineTable({
    inventoryId: v.id("inventory"),
    transactionType: v.union(v.literal("Purchase"), v.literal("Production"), v.literal("Sale"), v.literal("Adjustment")),
    transactionDate: v.string(),
    quantity: v.number(),
    unitCost: v.number(),
    totalCost: v.number(),
    remainingQuantity: v.number(),
    remainingCost: v.number(),
    referenceNumber: v.optional(v.string()),
    isActive: v.boolean(),
  }),

  // Inventory Transactions for Audit Trail
  inventoryTransactions: defineTable({
    inventoryId: v.id("inventory"),
    transactionType: v.union(
      v.literal("Purchase Receipt"),
      v.literal("Production Receipt"),
      v.literal("Sales Issue"),
      v.literal("Material Issue"),
      v.literal("Adjustment"),
      v.literal("Transfer")
    ),
    transactionDate: v.string(),
    quantityIn: v.number(),
    quantityOut: v.number(),
    unitCost: v.number(),
    totalCost: v.number(),
    runningBalance: v.number(),
    referenceNumber: v.string(),
    description: v.string(),
    journalEntryId: v.optional(v.id("journalEntries")),
  }),



  suppliers: defineTable({
    name: v.string(),
    contactEmail: v.string(),
    phone: v.string(),
    address: v.string(),
    supplierType: v.union(v.literal("Raw Material"), v.literal("Components"), v.literal("Services"), v.literal("Equipment")),
    paymentTerms: v.string(),
    qualityRating: v.number(), // 1-5 scale
  }),

  purchaseOrders: defineTable({
    supplierId: v.id("suppliers"),
    orderNumber: v.string(),
    orderDate: v.string(),
    expectedDelivery: v.string(),
    totalAmount: v.number(),
    status: v.union(v.literal("Draft"), v.literal("Sent"), v.literal("Confirmed"), v.literal("Delivered"), v.literal("Cancelled")),
  }),

  productionOrders: defineTable({
    orderNumber: v.string(),
    productId: v.id("inventory"),
    quantityOrdered: v.number(),
    quantityProduced: v.number(),
    startDate: v.string(),
    plannedEndDate: v.string(),
    actualEndDate: v.optional(v.string()),
    status: v.union(v.literal("Planned"), v.literal("In Progress"), v.literal("Completed"), v.literal("On Hold"), v.literal("Cancelled")),
    priority: v.union(v.literal("Low"), v.literal("Medium"), v.literal("High"), v.literal("Urgent")),
  }),

  employees: defineTable({
    employeeId: v.string(),
    firstName: v.string(),
    lastName: v.string(),
    email: v.string(),
    phone: v.string(),
    department: v.union(v.literal("Production"), v.literal("Quality Control"), v.literal("Maintenance"), v.literal("Finance"), v.literal("HR"), v.literal("Sales"), v.literal("Engineering")),
    position: v.string(),
    hireDate: v.string(),
    salary: v.number(),
    status: v.union(v.literal("Active"), v.literal("Inactive"), v.literal("On Leave")),
  }),

  workOrders: defineTable({
    workOrderNumber: v.string(),
    productionOrderId: v.id("productionOrders"),
    workstationId: v.string(),
    assignedEmployeeId: v.id("employees"),
    operation: v.string(),
    plannedStartTime: v.string(),
    plannedEndTime: v.string(),
    actualStartTime: v.optional(v.string()),
    actualEndTime: v.optional(v.string()),
    status: v.union(v.literal("Scheduled"), v.literal("In Progress"), v.literal("Completed"), v.literal("Paused")),
  }),

  qualityChecks: defineTable({
    productionOrderId: v.id("productionOrders"),
    inspectorId: v.id("employees"),
    checkDate: v.string(),
    checkType: v.union(v.literal("Incoming"), v.literal("In-Process"), v.literal("Final"), v.literal("Customer Return")),
    result: v.union(v.literal("Pass"), v.literal("Fail"), v.literal("Conditional Pass")),
    defectsFound: v.optional(v.string()),
    correctionRequired: v.optional(v.string()),
  }),

  machines: defineTable({
    machineId: v.string(),
    machineName: v.string(),
    type: v.string(),
    location: v.string(),
    status: v.union(v.literal("Running"), v.literal("Idle"), v.literal("Maintenance"), v.literal("Breakdown")),
    lastMaintenanceDate: v.string(),
    nextMaintenanceDate: v.string(),
    efficiency: v.number(), // percentage
  }),
});