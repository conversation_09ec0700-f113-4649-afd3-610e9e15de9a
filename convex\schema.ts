import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  accounts: defineTable({
    name: v.string(),
    type: v.union(
      v.literal("Asset"),
      v.literal("Liability"),
      v.literal("Equity"),
      v.literal("Revenue"),
      v.literal("Expense")
    ),
    code: v.string(),
    subType: v.optional(v.union(
      v.literal("Current Asset"), v.literal("Fixed Asset"), v.literal("Inventory"),
      v.literal("Current Liability"), v.literal("Long-term Liability"),
      v.literal("Operating Revenue"), v.literal("Non-operating Revenue"),
      v.literal("Cost of Goods Sold"), v.literal("Operating Expense"), v.literal("Administrative Expense")
    )),
    parentAccountId: v.optional(v.id("accounts")),
    isActive: v.optional(v.boolean()), // For backward compatibility
  }),

  journalEntries: defineTable({
    accountId: v.id("accounts"),
    date: v.string(), // Use ISO string for date
    description: v.string(),
    debit: v.number(),
    credit: v.number(),
    reference: v.optional(v.string()),
    transactionType: v.optional(v.union(
      v.literal("Sales"), v.literal("Purchase"), v.literal("Production"),
      v.literal("Payroll"), v.literal("Depreciation"), v.literal("Adjustment"),
      v.literal("Revenue Recognition"), v.literal("Payment")
    )),
    departmentId: v.optional(v.string()),
    costCenterId: v.optional(v.string()),
    projectId: v.optional(v.string()),
  }),

  invoices: defineTable({
    customerId: v.id("customers"),
    invoiceNumber: v.string(),
    dateIssued: v.string(), // Use ISO string for date
    dueDate: v.string(),    // Use ISO string for date
    totalAmount: v.number(),
    status: v.union(
      v.literal("Pending"),
      v.literal("Paid"),
      v.literal("Overdue")
    ),
  }),

  payments: defineTable({
    invoiceId: v.id("customerInvoices"),
    date: v.string(), // Use ISO string for date
    amount: v.number(),
    method: v.union(
      v.literal("Cash"),
      v.literal("Credit Card"),
      v.literal("Bank Transfer"),
      v.literal("Check")
    ),
    reference: v.optional(v.string()),
  }),

  customers: defineTable({
    name: v.string(),
    contactEmail: v.string(),
    phone: v.optional(v.string()),
    address: v.optional(v.string()),
    customerType: v.optional(v.union(v.literal("OEM"), v.literal("Aftermarket"), v.literal("Distributor"))),
    creditLimit: v.optional(v.number()),
    paymentTerms: v.optional(v.string()),
  }),

  // Enhanced Financial Tables for Manufacturing
  costCenters: defineTable({
    code: v.string(),
    name: v.string(),
    type: v.union(v.literal("Production"), v.literal("Service"), v.literal("Administrative")),
    managerId: v.optional(v.id("employees")),
    budgetAmount: v.optional(v.number()),
    isActive: v.boolean(),
  }),

  budgets: defineTable({
    name: v.string(),
    fiscalYear: v.string(),
    accountId: v.id("accounts"),
    costCenterId: v.optional(v.id("costCenters")),
    budgetedAmount: v.number(),
    actualAmount: v.number(),
    variance: v.number(),
    period: v.union(v.literal("Monthly"), v.literal("Quarterly"), v.literal("Yearly")),
  }),

  // Enhanced Fixed Assets for PAS 16 Compliance
  fixedAssets: defineTable({
    assetNumber: v.string(),
    assetName: v.string(),
    category: v.union(v.literal("Machinery"), v.literal("Equipment"), v.literal("Building"), v.literal("Vehicle"), v.literal("IT Equipment")),
    purchaseDate: v.string(),
    purchaseCost: v.number(),
    depreciationMethod: v.union(v.literal("Straight Line"), v.literal("Declining Balance"), v.literal("Units of Production")),
    usefulLife: v.number(), // in years
    salvageValue: v.number(),
    accumulatedDepreciation: v.number(),
    currentBookValue: v.number(),
    location: v.string(),
    status: v.union(v.literal("Active"), v.literal("Disposed"), v.literal("Under Maintenance")),
    // PAS 16 Additional Fields
    componentDepreciation: v.optional(v.boolean()),
    revaluationModel: v.optional(v.boolean()),
    revaluationDate: v.optional(v.string()),
    revaluationAmount: v.optional(v.number()),
    impairmentLoss: v.optional(v.number()),
    impairmentDate: v.optional(v.string()),
    disposalDate: v.optional(v.string()),
    disposalAmount: v.optional(v.number()),
    gainLossOnDisposal: v.optional(v.number()),
    lastDepreciationDate: v.optional(v.string()),
    nextDepreciationDate: v.optional(v.string()),
    monthlyDepreciation: v.optional(v.number()),
    depreciationAccountId: v.optional(v.id("accounts")),
    accumulatedDepreciationAccountId: v.optional(v.id("accounts")),
  }),

  // Depreciation Schedule for PAS 16
  depreciationSchedule: defineTable({
    assetId: v.id("fixedAssets"),
    periodDate: v.string(),
    openingBookValue: v.number(),
    depreciationExpense: v.number(),
    accumulatedDepreciation: v.number(),
    closingBookValue: v.number(),
    isPosted: v.boolean(),
    journalEntryId: v.optional(v.id("journalEntries")),
  }),

  // Manufacturing Cost Tracking
  workOrderCosts: defineTable({
    workOrderId: v.id("workOrders"),
    costType: v.union(v.literal("Material"), v.literal("Labor"), v.literal("Overhead")),
    accountId: v.id("accounts"),
    amount: v.number(),
    quantity: v.optional(v.number()),
    unitCost: v.optional(v.number()),
    date: v.string(),
    description: v.string(),
  }),

  // Financial Periods for Reporting
  fiscalPeriods: defineTable({
    periodName: v.string(),
    startDate: v.string(),
    endDate: v.string(),
    fiscalYear: v.string(),
    quarter: v.optional(v.number()),
    month: v.optional(v.number()),
    isClosed: v.boolean(),
  }),

  // Accounts Payable
  vendorInvoices: defineTable({
    supplierId: v.id("suppliers"),
    invoiceNumber: v.string(),
    invoiceDate: v.string(),
    dueDate: v.string(),
    totalAmount: v.number(),
    paidAmount: v.number(),
    status: v.union(v.literal("Pending"), v.literal("Approved"), v.literal("Paid"), v.literal("Overdue")),
    purchaseOrderId: v.optional(v.string()),
    description: v.string(),
  }),

  // Accounts Receivable - Enhanced for PFRS 15 Compliance
  customerInvoices: defineTable({
    customerId: v.id("customers"),
    invoiceNumber: v.string(),
    invoiceDate: v.string(),
    dueDate: v.string(),
    totalAmount: v.number(),
    paidAmount: v.number(),
    status: v.union(v.literal("Draft"), v.literal("Sent"), v.literal("Paid"), v.literal("Overdue"), v.literal("Cancelled")),
    salesOrderId: v.optional(v.string()),
    taxAmount: v.number(),
    discountAmount: v.number(),
    // PFRS 15 Revenue Recognition fields
    revenueRecognitionStatus: v.optional(v.union(
      v.literal("Not Recognized"),
      v.literal("Partially Recognized"),
      v.literal("Fully Recognized")
    )),
    revenueRecognizedAmount: v.optional(v.number()),
    performanceObligationsFulfilled: v.optional(v.boolean()),
    deliveryDate: v.optional(v.string()),
    controlTransferredDate: v.optional(v.string()),
    // Philippine VAT Compliance
    vatableAmount: v.optional(v.number()),
    vatExemptAmount: v.optional(v.number()),
    zeroRatedAmount: v.optional(v.number()),
    inputVatAmount: v.optional(v.number()),
    outputVatAmount: v.optional(v.number()),
    withholdingTaxAmount: v.optional(v.number()),
  }),

  // PFRS 15 Performance Obligations
  performanceObligations: defineTable({
    invoiceId: v.id("customerInvoices"),
    description: v.string(),
    allocatedAmount: v.number(),
    status: v.union(v.literal("Pending"), v.literal("In Progress"), v.literal("Satisfied")),
    satisfactionDate: v.optional(v.string()),
    satisfactionMethod: v.optional(v.union(v.literal("Point in Time"), v.literal("Over Time"))),
    percentageComplete: v.optional(v.number()),
  }),

  // Contract Liabilities (Deferred Revenue) - PFRS 15
  contractLiabilities: defineTable({
    customerId: v.id("customers"),
    contractNumber: v.string(),
    description: v.string(),
    totalContractValue: v.number(),
    advancePaymentReceived: v.number(),
    revenueRecognized: v.number(),
    remainingLiability: v.number(),
    contractDate: v.string(),
    expectedCompletionDate: v.string(),
    status: v.union(v.literal("Active"), v.literal("Completed"), v.literal("Cancelled")),
  }),

  // Manufacturing Tables - Enhanced for PFRS/PAS 2 Inventory Compliance
  inventory: defineTable({
    partNumber: v.string(),
    partName: v.string(),
    category: v.union(v.literal("Raw Material"), v.literal("Work in Progress"), v.literal("Finished Goods"), v.literal("Spare Parts")),
    currentStock: v.number(),
    minimumStock: v.number(),
    maximumStock: v.number(),
    unitCost: v.number(), // Weighted average or FIFO cost
    location: v.string(),
    supplierId: v.optional(v.id("suppliers")),
    // PFRS/PAS 2 Inventory Costing
    costingMethod: v.union(v.literal("FIFO"), v.literal("Weighted Average")),
    lastCostUpdate: v.optional(v.string()),
    standardCost: v.optional(v.number()),
    averageCost: v.optional(v.number()),
  }),

  // Inventory Cost Layers for FIFO/Weighted Average
  inventoryCostLayers: defineTable({
    inventoryId: v.id("inventory"),
    transactionType: v.union(v.literal("Purchase"), v.literal("Production"), v.literal("Sale"), v.literal("Adjustment")),
    transactionDate: v.string(),
    quantity: v.number(),
    unitCost: v.number(),
    totalCost: v.number(),
    remainingQuantity: v.number(),
    remainingCost: v.number(),
    referenceNumber: v.optional(v.string()),
    isActive: v.boolean(),
  }),

  // Inventory Transactions for Audit Trail
  inventoryTransactions: defineTable({
    inventoryId: v.id("inventory"),
    transactionType: v.union(
      v.literal("Purchase Receipt"),
      v.literal("Production Receipt"),
      v.literal("Sales Issue"),
      v.literal("Material Issue"),
      v.literal("Adjustment"),
      v.literal("Transfer")
    ),
    transactionDate: v.string(),
    quantityIn: v.number(),
    quantityOut: v.number(),
    unitCost: v.number(),
    totalCost: v.number(),
    runningBalance: v.number(),
    referenceNumber: v.string(),
    description: v.string(),
    journalEntryId: v.optional(v.id("journalEntries")),
  }),

  // Philippine Tax Compliance Tables
  vatTransactions: defineTable({
    transactionDate: v.string(),
    transactionType: v.union(v.literal("Sales"), v.literal("Purchase")),
    documentType: v.union(
      v.literal("Sales Invoice"),
      v.literal("Official Receipt"),
      v.literal("Purchase Invoice"),
      v.literal("Credit Memo"),
      v.literal("Debit Memo")
    ),
    documentNumber: v.string(),
    customerId: v.optional(v.id("customers")),
    supplierId: v.optional(v.id("suppliers")),
    vatableAmount: v.number(),
    vatExemptAmount: v.number(),
    zeroRatedAmount: v.number(),
    vatAmount: v.number(),
    vatRate: v.number(), // 12% for Philippines
    withholdingTaxAmount: v.number(),
    withholdingTaxRate: v.number(),
    netAmount: v.number(),
    totalAmount: v.number(),
    journalEntryId: v.optional(v.id("journalEntries")),
  }),

  // BIR Form Tracking
  birForms: defineTable({
    formType: v.union(
      v.literal("2550M"), // Monthly VAT
      v.literal("2550Q"), // Quarterly VAT
      v.literal("1601E"), // Monthly Withholding Tax
      v.literal("1601Q"), // Quarterly Withholding Tax
      v.literal("2307"), // Certificate of Creditable Tax Withheld
      v.literal("1702Q"), // Quarterly Income Tax
      v.literal("1702A") // Annual Income Tax
    ),
    periodCovered: v.string(),
    filingDate: v.string(),
    dueDate: v.string(),
    status: v.union(v.literal("Draft"), v.literal("Filed"), v.literal("Paid"), v.literal("Overdue")),
    totalTaxDue: v.number(),
    totalTaxPaid: v.number(),
    penalties: v.optional(v.number()),
    interest: v.optional(v.number()),
    referenceNumber: v.optional(v.string()),
  }),

  suppliers: defineTable({
    name: v.string(),
    contactEmail: v.string(),
    phone: v.string(),
    address: v.string(),
    supplierType: v.union(v.literal("Raw Material"), v.literal("Components"), v.literal("Services"), v.literal("Equipment")),
    paymentTerms: v.string(),
    qualityRating: v.number(), // 1-5 scale
  }),

  purchaseOrders: defineTable({
    supplierId: v.id("suppliers"),
    orderNumber: v.string(),
    orderDate: v.string(),
    expectedDelivery: v.string(),
    totalAmount: v.number(),
    status: v.union(v.literal("Draft"), v.literal("Sent"), v.literal("Confirmed"), v.literal("Delivered"), v.literal("Cancelled")),
  }),

  productionOrders: defineTable({
    orderNumber: v.string(),
    productId: v.id("inventory"),
    quantityOrdered: v.number(),
    quantityProduced: v.number(),
    startDate: v.string(),
    plannedEndDate: v.string(),
    actualEndDate: v.optional(v.string()),
    status: v.union(v.literal("Planned"), v.literal("In Progress"), v.literal("Completed"), v.literal("On Hold"), v.literal("Cancelled")),
    priority: v.union(v.literal("Low"), v.literal("Medium"), v.literal("High"), v.literal("Urgent")),
  }),

  employees: defineTable({
    employeeId: v.string(),
    firstName: v.string(),
    lastName: v.string(),
    email: v.string(),
    phone: v.string(),
    department: v.union(v.literal("Production"), v.literal("Quality Control"), v.literal("Maintenance"), v.literal("Finance"), v.literal("HR"), v.literal("Sales"), v.literal("Engineering")),
    position: v.string(),
    hireDate: v.string(),
    salary: v.number(),
    status: v.union(v.literal("Active"), v.literal("Inactive"), v.literal("On Leave")),
  }),

  workOrders: defineTable({
    workOrderNumber: v.string(),
    productionOrderId: v.id("productionOrders"),
    workstationId: v.string(),
    assignedEmployeeId: v.id("employees"),
    operation: v.string(),
    plannedStartTime: v.string(),
    plannedEndTime: v.string(),
    actualStartTime: v.optional(v.string()),
    actualEndTime: v.optional(v.string()),
    status: v.union(v.literal("Scheduled"), v.literal("In Progress"), v.literal("Completed"), v.literal("Paused")),
  }),

  qualityChecks: defineTable({
    productionOrderId: v.id("productionOrders"),
    inspectorId: v.id("employees"),
    checkDate: v.string(),
    checkType: v.union(v.literal("Incoming"), v.literal("In-Process"), v.literal("Final"), v.literal("Customer Return")),
    result: v.union(v.literal("Pass"), v.literal("Fail"), v.literal("Conditional Pass")),
    defectsFound: v.optional(v.string()),
    correctionRequired: v.optional(v.string()),
  }),

  machines: defineTable({
    machineId: v.string(),
    machineName: v.string(),
    type: v.string(),
    location: v.string(),
    status: v.union(v.literal("Running"), v.literal("Idle"), v.literal("Maintenance"), v.literal("Breakdown")),
    lastMaintenanceDate: v.string(),
    nextMaintenanceDate: v.string(),
    efficiency: v.number(), // percentage
  }),
});