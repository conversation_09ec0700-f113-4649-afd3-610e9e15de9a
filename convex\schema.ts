import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  accounts: defineTable({
    name: v.string(),
    type: v.union(
      v.literal("Asset"),
      v.literal("Liability"),
      v.literal("Equity"),
      v.literal("Revenue"),
      v.literal("Expense")
    ),
    code: v.string(),
    subType: v.optional(v.union(
      v.literal("Current Asset"), v.literal("Fixed Asset"), v.literal("Inventory"),
      v.literal("Current Liability"), v.literal("Long-term Liability"),
      v.literal("Operating Revenue"), v.literal("Non-operating Revenue"),
      v.literal("Cost of Goods Sold"), v.literal("Operating Expense"), v.literal("Administrative Expense")
    )),
    parentAccountId: v.optional(v.id("accounts")),
  }),

  journalEntries: defineTable({
    accountId: v.id("accounts"),
    date: v.string(), // Use ISO string for date
    description: v.string(),
    debit: v.number(),
    credit: v.number(),
    reference: v.optional(v.string()),
    transactionType: v.optional(v.union(
      v.literal("Sales"), v.literal("Purchase"), v.literal("Production"),
      v.literal("Payroll"), v.literal("Depreciation"), v.literal("Adjustment")
    )),
    departmentId: v.optional(v.string()),
    costCenterId: v.optional(v.string()),
    projectId: v.optional(v.string()),
  }),

  invoices: defineTable({
    customerId: v.id("customers"),
    invoiceNumber: v.string(),
    dateIssued: v.string(), // Use ISO string for date
    dueDate: v.string(),    // Use ISO string for date
    totalAmount: v.number(),
    status: v.union(
      v.literal("Pending"),
      v.literal("Paid"),
      v.literal("Overdue")
    ),
  }),

  payments: defineTable({
    invoiceId: v.id("invoices"),
    date: v.string(), // Use ISO string for date
    amount: v.number(),
    method: v.union(
      v.literal("Cash"),
      v.literal("Credit Card"),
      v.literal("Bank Transfer"),
      v.literal("Check")
    ),
    reference: v.optional(v.string()),
  }),

  customers: defineTable({
    name: v.string(),
    contactEmail: v.string(),
    phone: v.optional(v.string()),
    address: v.optional(v.string()),
    customerType: v.optional(v.union(v.literal("OEM"), v.literal("Aftermarket"), v.literal("Distributor"))),
    creditLimit: v.optional(v.number()),
    paymentTerms: v.optional(v.string()),
  }),

  // Enhanced Financial Tables for Manufacturing
  costCenters: defineTable({
    code: v.string(),
    name: v.string(),
    type: v.union(v.literal("Production"), v.literal("Service"), v.literal("Administrative")),
    managerId: v.optional(v.id("employees")),
    budgetAmount: v.optional(v.number()),
    isActive: v.boolean(),
  }),

  budgets: defineTable({
    name: v.string(),
    fiscalYear: v.string(),
    accountId: v.id("accounts"),
    costCenterId: v.optional(v.id("costCenters")),
    budgetedAmount: v.number(),
    actualAmount: v.number(),
    variance: v.number(),
    period: v.union(v.literal("Monthly"), v.literal("Quarterly"), v.literal("Yearly")),
  }),

  fixedAssets: defineTable({
    assetNumber: v.string(),
    assetName: v.string(),
    category: v.union(v.literal("Machinery"), v.literal("Equipment"), v.literal("Building"), v.literal("Vehicle"), v.literal("IT Equipment")),
    purchaseDate: v.string(),
    purchaseCost: v.number(),
    depreciationMethod: v.union(v.literal("Straight Line"), v.literal("Declining Balance"), v.literal("Units of Production")),
    usefulLife: v.number(), // in years
    salvageValue: v.number(),
    accumulatedDepreciation: v.number(),
    currentBookValue: v.number(),
    location: v.string(),
    status: v.union(v.literal("Active"), v.literal("Disposed"), v.literal("Under Maintenance")),
  }),

  // Manufacturing Cost Tracking
  workOrderCosts: defineTable({
    workOrderId: v.id("workOrders"),
    costType: v.union(v.literal("Material"), v.literal("Labor"), v.literal("Overhead")),
    accountId: v.id("accounts"),
    amount: v.number(),
    quantity: v.optional(v.number()),
    unitCost: v.optional(v.number()),
    date: v.string(),
    description: v.string(),
  }),

  // Financial Periods for Reporting
  fiscalPeriods: defineTable({
    periodName: v.string(),
    startDate: v.string(),
    endDate: v.string(),
    fiscalYear: v.string(),
    quarter: v.optional(v.number()),
    month: v.optional(v.number()),
    isClosed: v.boolean(),
  }),

  // Accounts Payable
  vendorInvoices: defineTable({
    supplierId: v.id("suppliers"),
    invoiceNumber: v.string(),
    invoiceDate: v.string(),
    dueDate: v.string(),
    totalAmount: v.number(),
    paidAmount: v.number(),
    status: v.union(v.literal("Pending"), v.literal("Approved"), v.literal("Paid"), v.literal("Overdue")),
    purchaseOrderId: v.optional(v.id("purchaseOrders")),
    description: v.string(),
  }),

  // Accounts Receivable
  customerInvoices: defineTable({
    customerId: v.id("customers"),
    invoiceNumber: v.string(),
    invoiceDate: v.string(),
    dueDate: v.string(),
    totalAmount: v.number(),
    paidAmount: v.number(),
    status: v.union(v.literal("Draft"), v.literal("Sent"), v.literal("Paid"), v.literal("Overdue"), v.literal("Cancelled")),
    salesOrderId: v.optional(v.string()),
    taxAmount: v.number(),
    discountAmount: v.number(),
  }),

  // Manufacturing Tables (from previous schema)
  inventory: defineTable({
    partNumber: v.string(),
    partName: v.string(),
    category: v.union(v.literal("Raw Material"), v.literal("Work in Progress"), v.literal("Finished Goods"), v.literal("Spare Parts")),
    currentStock: v.number(),
    minimumStock: v.number(),
    maximumStock: v.number(),
    unitCost: v.number(),
    location: v.string(),
    supplierId: v.optional(v.id("suppliers")),
  }),

  suppliers: defineTable({
    name: v.string(),
    contactEmail: v.string(),
    phone: v.string(),
    address: v.string(),
    supplierType: v.union(v.literal("Raw Material"), v.literal("Components"), v.literal("Services"), v.literal("Equipment")),
    paymentTerms: v.string(),
    qualityRating: v.number(), // 1-5 scale
  }),

  purchaseOrders: defineTable({
    supplierId: v.id("suppliers"),
    orderNumber: v.string(),
    orderDate: v.string(),
    expectedDelivery: v.string(),
    totalAmount: v.number(),
    status: v.union(v.literal("Draft"), v.literal("Sent"), v.literal("Confirmed"), v.literal("Delivered"), v.literal("Cancelled")),
  }),

  productionOrders: defineTable({
    orderNumber: v.string(),
    productId: v.id("inventory"),
    quantityOrdered: v.number(),
    quantityProduced: v.number(),
    startDate: v.string(),
    plannedEndDate: v.string(),
    actualEndDate: v.optional(v.string()),
    status: v.union(v.literal("Planned"), v.literal("In Progress"), v.literal("Completed"), v.literal("On Hold"), v.literal("Cancelled")),
    priority: v.union(v.literal("Low"), v.literal("Medium"), v.literal("High"), v.literal("Urgent")),
  }),

  employees: defineTable({
    employeeId: v.string(),
    firstName: v.string(),
    lastName: v.string(),
    email: v.string(),
    phone: v.string(),
    department: v.union(v.literal("Production"), v.literal("Quality Control"), v.literal("Maintenance"), v.literal("Finance"), v.literal("HR"), v.literal("Sales"), v.literal("Engineering")),
    position: v.string(),
    hireDate: v.string(),
    salary: v.number(),
    status: v.union(v.literal("Active"), v.literal("Inactive"), v.literal("On Leave")),
  }),

  workOrders: defineTable({
    workOrderNumber: v.string(),
    productionOrderId: v.id("productionOrders"),
    workstationId: v.string(),
    assignedEmployeeId: v.id("employees"),
    operation: v.string(),
    plannedStartTime: v.string(),
    plannedEndTime: v.string(),
    actualStartTime: v.optional(v.string()),
    actualEndTime: v.optional(v.string()),
    status: v.union(v.literal("Scheduled"), v.literal("In Progress"), v.literal("Completed"), v.literal("Paused")),
  }),

  qualityChecks: defineTable({
    productionOrderId: v.id("productionOrders"),
    inspectorId: v.id("employees"),
    checkDate: v.string(),
    checkType: v.union(v.literal("Incoming"), v.literal("In-Process"), v.literal("Final"), v.literal("Customer Return")),
    result: v.union(v.literal("Pass"), v.literal("Fail"), v.literal("Conditional Pass")),
    defectsFound: v.optional(v.string()),
    correctionRequired: v.optional(v.string()),
  }),

  machines: defineTable({
    machineId: v.string(),
    machineName: v.string(),
    type: v.string(),
    location: v.string(),
    status: v.union(v.literal("Running"), v.literal("Idle"), v.literal("Maintenance"), v.literal("Breakdown")),
    lastMaintenanceDate: v.string(),
    nextMaintenanceDate: v.string(),
    efficiency: v.number(), // percentage
  }),
});