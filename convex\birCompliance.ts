import { query, mutation } from "./_generated/server";
import { v } from "convex/values";

// ============================================================================
// BIR COMPLIANCE AND VAT MANAGEMENT SYSTEM
// Philippine Tax Compliance • 12% VAT • Withholding Tax • BIR Forms
// ============================================================================

// Generate VAT Summary Report for BIR Filing
export const getVATSummaryReport = query({
  args: {
    startDate: v.string(),
    endDate: v.string(),
    fiscalPeriodId: v.optional(v.id("fiscalPeriods"))
  },
  handler: async (ctx, args) => {
    const vatTransactions = await ctx.db.query("vatTransactions")
      .filter(q => q.and(
        q.gte(q.field("transactionDate"), args.startDate),
        q.lte(q.field("transactionDate"), args.endDate)
      ))
      .collect();

    const salesTransactions = vatTransactions.filter(vt => vt.transactionType === "Sales");
    const purchaseTransactions = vatTransactions.filter(vt => vt.transactionType === "Purchase");

    // Sales VAT Summary
    const salesSummary = {
      totalVatableSales: salesTransactions.reduce((sum, vt) => sum + vt.vatableAmount, 0),
      totalVatExemptSales: salesTransactions.reduce((sum, vt) => sum + vt.vatExemptAmount, 0),
      totalZeroRatedSales: salesTransactions.reduce((sum, vt) => sum + vt.zeroRatedAmount, 0),
      totalOutputVAT: salesTransactions.reduce((sum, vt) => sum + vt.vatAmount, 0),
      totalSales: salesTransactions.reduce((sum, vt) => sum + vt.totalAmount, 0),
      transactionCount: salesTransactions.length
    };

    // Purchase VAT Summary
    const purchaseSummary = {
      totalVatablePurchases: purchaseTransactions.reduce((sum, vt) => sum + vt.vatableAmount, 0),
      totalVatExemptPurchases: purchaseTransactions.reduce((sum, vt) => sum + vt.vatExemptAmount, 0),
      totalZeroRatedPurchases: purchaseTransactions.reduce((sum, vt) => sum + vt.zeroRatedAmount, 0),
      totalInputVAT: purchaseTransactions.reduce((sum, vt) => sum + vt.vatAmount, 0),
      totalWithholdingTax: purchaseTransactions.reduce((sum, vt) => sum + vt.withholdingTaxAmount, 0),
      totalPurchases: purchaseTransactions.reduce((sum, vt) => sum + vt.totalAmount, 0),
      transactionCount: purchaseTransactions.length
    };

    // VAT Payable/Refundable Calculation
    const netVATPosition = salesSummary.totalOutputVAT - purchaseSummary.totalInputVAT;
    const vatPayable = Math.max(0, netVATPosition);
    const vatRefundable = Math.max(0, -netVATPosition);

    // BIR Form 2550M/Q Data
    const birForm2550Data = {
      vatableSales: salesSummary.totalVatableSales,
      vatExemptSales: salesSummary.totalVatExemptSales,
      zeroRatedSales: salesSummary.totalZeroRatedSales,
      outputVAT: salesSummary.totalOutputVAT,
      vatablePurchases: purchaseSummary.totalVatablePurchases,
      inputVAT: purchaseSummary.totalInputVAT,
      netVAT: netVATPosition,
      vatPayable: vatPayable,
      vatRefundable: vatRefundable
    };

    return {
      reportTitle: "VAT Summary Report",
      companyName: "XYZ Manufacturing Ltd.",
      period: { startDate: args.startDate, endDate: args.endDate },
      currency: "PHP",
      salesSummary,
      purchaseSummary,
      vatPosition: {
        netVATPosition,
        vatPayable,
        vatRefundable,
        isPayable: netVATPosition > 0,
        isRefundable: netVATPosition < 0
      },
      birForm2550Data,
      totalTransactions: vatTransactions.length,
      birCompliant: true,
      generatedAt: new Date().toISOString()
    };
  }
});

// Generate Withholding Tax Summary Report
export const getWithholdingTaxReport = query({
  args: {
    startDate: v.string(),
    endDate: v.string(),
    fiscalPeriodId: v.optional(v.id("fiscalPeriods"))
  },
  handler: async (ctx, args) => {
    const vatTransactions = await ctx.db.query("vatTransactions")
      .filter(q => q.and(
        q.gte(q.field("transactionDate"), args.startDate),
        q.lte(q.field("transactionDate"), args.endDate),
        q.gt(q.field("withholdingTaxAmount"), 0)
      ))
      .collect();

    // Group by withholding tax rate
    const withholdingTaxByRate = new Map<number, {
      rate: number,
      totalAmount: number,
      totalTax: number,
      transactionCount: number,
      transactions: any[]
    }>();

    vatTransactions.forEach(vt => {
      const rate = vt.withholdingTaxRate;
      if (!withholdingTaxByRate.has(rate)) {
        withholdingTaxByRate.set(rate, {
          rate: rate,
          totalAmount: 0,
          totalTax: 0,
          transactionCount: 0,
          transactions: []
        });
      }
      
      const group = withholdingTaxByRate.get(rate)!;
      group.totalAmount += vt.vatableAmount;
      group.totalTax += vt.withholdingTaxAmount;
      group.transactionCount += 1;
      group.transactions.push({
        documentNumber: vt.documentNumber,
        transactionDate: vt.transactionDate,
        amount: vt.vatableAmount,
        withholdingTax: vt.withholdingTaxAmount
      });
    });

    const summary = Array.from(withholdingTaxByRate.values()).map(group => ({
      withholdingTaxRate: (group.rate * 100).toFixed(1) + "%",
      totalAmount: group.totalAmount,
      totalWithholdingTax: group.totalTax,
      transactionCount: group.transactionCount,
      averageRate: group.totalAmount > 0 ? (group.totalTax / group.totalAmount) * 100 : 0
    }));

    const totalWithholdingTax = vatTransactions.reduce((sum, vt) => sum + vt.withholdingTaxAmount, 0);
    const totalAmount = vatTransactions.reduce((sum, vt) => sum + vt.vatableAmount, 0);

    return {
      reportTitle: "Withholding Tax Report",
      companyName: "XYZ Manufacturing Ltd.",
      period: { startDate: args.startDate, endDate: args.endDate },
      currency: "PHP",
      summary,
      totals: {
        totalAmount,
        totalWithholdingTax,
        averageRate: totalAmount > 0 ? (totalWithholdingTax / totalAmount) * 100 : 0,
        transactionCount: vatTransactions.length
      },
      birCompliant: true,
      generatedAt: new Date().toISOString()
    };
  }
});

// Create BIR Form Filing Record
export const createBIRFormFiling = mutation({
  args: {
    formType: v.union(
      v.literal("2550M"), v.literal("2550Q"), v.literal("1601E"), 
      v.literal("1601Q"), v.literal("2307"), v.literal("1702Q"), v.literal("1702A")
    ),
    periodCovered: v.string(),
    filingDate: v.string(),
    dueDate: v.string(),
    totalTaxDue: v.number(),
    totalTaxPaid: v.number(),
    penalties: v.optional(v.number()),
    interest: v.optional(v.number()),
    referenceNumber: v.optional(v.string()),
    notes: v.optional(v.string())
  },
  handler: async (ctx, args) => {
    const birFormId = await ctx.db.insert("birForms", {
      formType: args.formType,
      periodCovered: args.periodCovered,
      filingDate: args.filingDate,
      dueDate: args.dueDate,
      status: args.totalTaxPaid >= args.totalTaxDue ? "Paid" : "Filed",
      totalTaxDue: args.totalTaxDue,
      totalTaxPaid: args.totalTaxPaid,
      penalties: args.penalties || 0,
      interest: args.interest || 0,
      referenceNumber: args.referenceNumber,
      filedBy: "System",
      notes: args.notes
    });

    const balance = args.totalTaxDue - args.totalTaxPaid;
    const status = balance <= 0 ? "Paid" : balance === args.totalTaxDue ? "Filed" : "Partially Paid";

    return {
      success: true,
      birFormId,
      message: `✅ BIR Form ${args.formType} filed successfully\n\n📋 Filing Details:\n• Period: ${args.periodCovered}\n• Tax Due: ₱${args.totalTaxDue.toLocaleString()}\n• Tax Paid: ₱${args.totalTaxPaid.toLocaleString()}\n• Balance: ₱${balance.toLocaleString()}\n• Status: ${status}`,
      formType: args.formType,
      periodCovered: args.periodCovered,
      totalTaxDue: args.totalTaxDue,
      totalTaxPaid: args.totalTaxPaid,
      balance: balance,
      status: status
    };
  }
});

// Get BIR Forms Status
export const getBIRFormsStatus = query({
  args: {
    fiscalYear: v.optional(v.number()),
    formType: v.optional(v.union(
      v.literal("2550M"), v.literal("2550Q"), v.literal("1601E"), 
      v.literal("1601Q"), v.literal("2307"), v.literal("1702Q"), v.literal("1702A")
    ))
  },
  handler: async (ctx, args) => {
    let query = ctx.db.query("birForms");
    
    if (args.formType) {
      query = query.filter(q => q.eq(q.field("formType"), args.formType));
    }

    const birForms = await query.collect();

    // Filter by fiscal year if provided
    const filteredForms = args.fiscalYear 
      ? birForms.filter(form => form.periodCovered.includes(args.fiscalYear!.toString()))
      : birForms;

    // Group by form type
    const formsByType = new Map<string, any[]>();
    filteredForms.forEach(form => {
      if (!formsByType.has(form.formType)) {
        formsByType.set(form.formType, []);
      }
      formsByType.get(form.formType)!.push(form);
    });

    // Calculate summary statistics
    const summary = Array.from(formsByType.entries()).map(([formType, forms]) => {
      const totalTaxDue = forms.reduce((sum, form) => sum + form.totalTaxDue, 0);
      const totalTaxPaid = forms.reduce((sum, form) => sum + form.totalTaxPaid, 0);
      const totalPenalties = forms.reduce((sum, form) => sum + (form.penalties || 0), 0);
      const totalInterest = forms.reduce((sum, form) => sum + (form.interest || 0), 0);
      
      const paidForms = forms.filter(form => form.status === "Paid").length;
      const filedForms = forms.filter(form => form.status === "Filed").length;
      const overdueForms = forms.filter(form => {
        const dueDate = new Date(form.dueDate);
        const today = new Date();
        return dueDate < today && form.status !== "Paid";
      }).length;

      return {
        formType,
        totalForms: forms.length,
        paidForms,
        filedForms,
        overdueForms,
        totalTaxDue,
        totalTaxPaid,
        totalPenalties,
        totalInterest,
        balance: totalTaxDue - totalTaxPaid,
        complianceRate: forms.length > 0 ? (paidForms / forms.length) * 100 : 0
      };
    });

    const overallTotals = {
      totalForms: filteredForms.length,
      totalTaxDue: filteredForms.reduce((sum, form) => sum + form.totalTaxDue, 0),
      totalTaxPaid: filteredForms.reduce((sum, form) => sum + form.totalTaxPaid, 0),
      totalPenalties: filteredForms.reduce((sum, form) => sum + (form.penalties || 0), 0),
      totalInterest: filteredForms.reduce((sum, form) => sum + (form.interest || 0), 0),
      overallBalance: 0
    };
    overallTotals.overallBalance = overallTotals.totalTaxDue - overallTotals.totalTaxPaid;

    return {
      reportTitle: "BIR Forms Status Report",
      companyName: "XYZ Manufacturing Ltd.",
      fiscalYear: args.fiscalYear,
      formType: args.formType,
      summary,
      overallTotals,
      forms: filteredForms.map(form => ({
        formType: form.formType,
        periodCovered: form.periodCovered,
        filingDate: form.filingDate,
        dueDate: form.dueDate,
        status: form.status,
        totalTaxDue: form.totalTaxDue,
        totalTaxPaid: form.totalTaxPaid,
        balance: form.totalTaxDue - form.totalTaxPaid,
        penalties: form.penalties || 0,
        interest: form.interest || 0,
        referenceNumber: form.referenceNumber,
        isOverdue: new Date(form.dueDate) < new Date() && form.status !== "Paid"
      })),
      birCompliant: true,
      generatedAt: new Date().toISOString()
    };
  }
});

// Calculate VAT for Transaction
export const calculateVAT = query({
  args: {
    amount: v.number(),
    vatType: v.union(v.literal("Vatable"), v.literal("VAT Exempt"), v.literal("Zero Rated")),
    vatRate: v.optional(v.number())
  },
  handler: async (ctx, args) => {
    const vatRate = args.vatRate || 0.12; // 12% Philippine VAT
    
    let vatableAmount = 0;
    let vatExemptAmount = 0;
    let zeroRatedAmount = 0;
    let vatAmount = 0;

    switch (args.vatType) {
      case "Vatable":
        vatableAmount = args.amount;
        vatAmount = args.amount * vatRate;
        break;
      case "VAT Exempt":
        vatExemptAmount = args.amount;
        vatAmount = 0;
        break;
      case "Zero Rated":
        zeroRatedAmount = args.amount;
        vatAmount = 0;
        break;
    }

    const totalAmount = args.amount + vatAmount;

    return {
      baseAmount: args.amount,
      vatType: args.vatType,
      vatRate: vatRate,
      vatableAmount,
      vatExemptAmount,
      zeroRatedAmount,
      vatAmount,
      totalAmount,
      vatPercentage: (vatRate * 100).toFixed(1) + "%",
      birCompliant: true
    };
  }
});
