// convex/finance/seedData.ts
import { mutation } from "../_generated/server";
import { v } from "convex/values";

export const seedAccounts = mutation(async (ctx) => {
  // Check if accounts already exist
  const existingAccounts = await ctx.db.query("accounts").collect();
  if (existingAccounts.length > 0) {
    return { message: "Accounts already exist", count: existingAccounts.length };
  }

  const accounts = [
    // Assets
    { name: "Cash", type: "Asset" as const, code: "1001" },
    { name: "Accounts Receivable", type: "Asset" as const, code: "1200" },
    { name: "Inventory", type: "Asset" as const, code: "1300" },
    { name: "Equipment", type: "Asset" as const, code: "1500" },
    { name: "Buildings", type: "Asset" as const, code: "1600" },
    
    // Liabilities
    { name: "Accounts Payable", type: "Liability" as const, code: "2001" },
    { name: "Notes Payable", type: "Liability" as const, code: "2100" },
    { name: "Accrued Expenses", type: "Liability" as const, code: "2200" },
    
    // Equity
    { name: "Owner's Equity", type: "Equity" as const, code: "3001" },
    { name: "Retained Earnings", type: "Equity" as const, code: "3200" },
    
    // Revenue
    { name: "Sales Revenue", type: "Revenue" as const, code: "4001" },
    { name: "Service Revenue", type: "Revenue" as const, code: "4100" },
    { name: "Interest Income", type: "Revenue" as const, code: "4200" },
    
    // Expenses
    { name: "Cost of Goods Sold", type: "Expense" as const, code: "5001" },
    { name: "Salaries Expense", type: "Expense" as const, code: "6001" },
    { name: "Rent Expense", type: "Expense" as const, code: "6100" },
    { name: "Utilities Expense", type: "Expense" as const, code: "6200" },
    { name: "Office Supplies", type: "Expense" as const, code: "6300" },
    { name: "Marketing Expense", type: "Expense" as const, code: "6400" },
    { name: "Insurance Expense", type: "Expense" as const, code: "6500" },
  ];

  const createdAccounts = [];
  for (const account of accounts) {
    const id = await ctx.db.insert("accounts", account);
    createdAccounts.push({ id, ...account });
  }

  return { message: "Accounts created successfully", count: createdAccounts.length, accounts: createdAccounts };
});

export const seedCustomers = mutation(async (ctx) => {
  // Check if customers already exist
  const existingCustomers = await ctx.db.query("customers").collect();
  if (existingCustomers.length > 0) {
    return { message: "Customers already exist", count: existingCustomers.length };
  }

  const customers = [
    {
      name: "ABC Manufacturing Corp",
      contactEmail: "<EMAIL>",
      phone: "******-0101",
      address: "123 Industrial Blvd, Manufacturing City, MC 12345"
    },
    {
      name: "XYZ Retail Solutions",
      contactEmail: "<EMAIL>",
      phone: "******-0102",
      address: "456 Commerce St, Retail Town, RT 67890"
    },
    {
      name: "Global Tech Industries",
      contactEmail: "<EMAIL>",
      phone: "******-0103",
      address: "789 Technology Ave, Tech City, TC 11111"
    },
    {
      name: "Local Services LLC",
      contactEmail: "<EMAIL>",
      phone: "******-0104",
      address: "321 Service Road, Service Village, SV 22222"
    },
    {
      name: "Premium Products Inc",
      contactEmail: "<EMAIL>",
      phone: "******-0105",
      address: "654 Premium Plaza, Luxury Lane, LL 33333"
    }
  ];

  const createdCustomers = [];
  for (const customer of customers) {
    const id = await ctx.db.insert("customers", customer);
    createdCustomers.push({ id, ...customer });
  }

  return { message: "Customers created successfully", count: createdCustomers.length, customers: createdCustomers };
});

export const seedJournalEntries = mutation(async (ctx) => {
  // Check if journal entries already exist
  const existingEntries = await ctx.db.query("journalEntries").collect();
  if (existingEntries.length > 0) {
    return { message: "Journal entries already exist", count: existingEntries.length };
  }

  // Get accounts to reference
  const accounts = await ctx.db.query("accounts").collect();
  if (accounts.length === 0) {
    throw new Error("No accounts found. Please seed accounts first.");
  }

  const getAccountByName = (name: string) => accounts.find(acc => acc.name === name);

  const cashAccount = getAccountByName("Cash");
  const salesRevenueAccount = getAccountByName("Sales Revenue");
  const serviceRevenueAccount = getAccountByName("Service Revenue");
  const salariesExpenseAccount = getAccountByName("Salaries Expense");
  const rentExpenseAccount = getAccountByName("Rent Expense");
  const utilitiesExpenseAccount = getAccountByName("Utilities Expense");
  const marketingExpenseAccount = getAccountByName("Marketing Expense");
  const accountsReceivableAccount = getAccountByName("Accounts Receivable");

  if (!cashAccount || !salesRevenueAccount || !serviceRevenueAccount || !salariesExpenseAccount || 
      !rentExpenseAccount || !utilitiesExpenseAccount || !marketingExpenseAccount || !accountsReceivableAccount) {
    throw new Error("Required accounts not found");
  }

  const journalEntries = [
    // Sales transactions
    { accountId: cashAccount._id, date: "2024-01-15", description: "Cash sale to ABC Manufacturing", debit: 15000, credit: 0, reference: "INV-001" },
    { accountId: salesRevenueAccount._id, date: "2024-01-15", description: "Cash sale to ABC Manufacturing", debit: 0, credit: 15000, reference: "INV-001" },
    
    { accountId: accountsReceivableAccount._id, date: "2024-01-20", description: "Credit sale to XYZ Retail", debit: 25000, credit: 0, reference: "INV-002" },
    { accountId: salesRevenueAccount._id, date: "2024-01-20", description: "Credit sale to XYZ Retail", debit: 0, credit: 25000, reference: "INV-002" },
    
    { accountId: cashAccount._id, date: "2024-01-25", description: "Service revenue from Global Tech", debit: 8000, credit: 0, reference: "SRV-001" },
    { accountId: serviceRevenueAccount._id, date: "2024-01-25", description: "Service revenue from Global Tech", debit: 0, credit: 8000, reference: "SRV-001" },
    
    // Expense transactions
    { accountId: salariesExpenseAccount._id, date: "2024-01-31", description: "Monthly salaries", debit: 12000, credit: 0, reference: "PAY-001" },
    { accountId: cashAccount._id, date: "2024-01-31", description: "Monthly salaries", debit: 0, credit: 12000, reference: "PAY-001" },
    
    { accountId: rentExpenseAccount._id, date: "2024-02-01", description: "Office rent for February", debit: 3000, credit: 0, reference: "RENT-002" },
    { accountId: cashAccount._id, date: "2024-02-01", description: "Office rent for February", debit: 0, credit: 3000, reference: "RENT-002" },
    
    { accountId: utilitiesExpenseAccount._id, date: "2024-02-05", description: "Electricity bill", debit: 800, credit: 0, reference: "UTIL-001" },
    { accountId: cashAccount._id, date: "2024-02-05", description: "Electricity bill", debit: 0, credit: 800, reference: "UTIL-001" },
    
    { accountId: marketingExpenseAccount._id, date: "2024-02-10", description: "Online advertising", debit: 2500, credit: 0, reference: "MKT-001" },
    { accountId: cashAccount._id, date: "2024-02-10", description: "Online advertising", debit: 0, credit: 2500, reference: "MKT-001" },
    
    // More recent transactions
    { accountId: cashAccount._id, date: "2024-02-15", description: "Payment received from XYZ Retail", debit: 25000, credit: 0, reference: "PMT-001" },
    { accountId: accountsReceivableAccount._id, date: "2024-02-15", description: "Payment received from XYZ Retail", debit: 0, credit: 25000, reference: "PMT-001" },
    
    { accountId: cashAccount._id, date: "2024-02-20", description: "Cash sale to Local Services", debit: 18000, credit: 0, reference: "INV-003" },
    { accountId: salesRevenueAccount._id, date: "2024-02-20", description: "Cash sale to Local Services", debit: 0, credit: 18000, reference: "INV-003" },
  ];

  const createdEntries = [];
  for (const entry of journalEntries) {
    const id = await ctx.db.insert("journalEntries", entry);
    createdEntries.push({ id, ...entry });
  }

  return { message: "Journal entries created successfully", count: createdEntries.length };
});

export const seedInvoices = mutation(async (ctx) => {
  // Check if invoices already exist
  const existingInvoices = await ctx.db.query("invoices").collect();
  if (existingInvoices.length > 0) {
    return { message: "Invoices already exist", count: existingInvoices.length };
  }

  // Get customers to reference
  const customers = await ctx.db.query("customers").collect();
  if (customers.length === 0) {
    throw new Error("No customers found. Please seed customers first.");
  }

  const getCustomerByName = (name: string) => customers.find(cust => cust.name === name);

  const abcManufacturing = getCustomerByName("ABC Manufacturing Corp");
  const xyzRetail = getCustomerByName("XYZ Retail Solutions");
  const globalTech = getCustomerByName("Global Tech Industries");
  const localServices = getCustomerByName("Local Services LLC");
  const premiumProducts = getCustomerByName("Premium Products Inc");

  if (!abcManufacturing || !xyzRetail || !globalTech || !localServices || !premiumProducts) {
    throw new Error("Required customers not found");
  }

  const invoices = [
    {
      customerId: abcManufacturing._id,
      invoiceNumber: "INV-001",
      dateIssued: "2024-01-15",
      dueDate: "2024-02-14",
      totalAmount: 15000,
      status: "Paid" as const
    },
    {
      customerId: xyzRetail._id,
      invoiceNumber: "INV-002",
      dateIssued: "2024-01-20",
      dueDate: "2024-02-19",
      totalAmount: 25000,
      status: "Paid" as const
    },
    {
      customerId: globalTech._id,
      invoiceNumber: "INV-003",
      dateIssued: "2024-01-25",
      dueDate: "2024-02-24",
      totalAmount: 8000,
      status: "Pending" as const
    },
    {
      customerId: localServices._id,
      invoiceNumber: "INV-004",
      dateIssued: "2024-02-01",
      dueDate: "2024-03-03",
      totalAmount: 18000,
      status: "Pending" as const
    },
    {
      customerId: premiumProducts._id,
      invoiceNumber: "INV-005",
      dateIssued: "2024-02-10",
      dueDate: "2024-03-12",
      totalAmount: 32000,
      status: "Pending" as const
    },
    {
      customerId: abcManufacturing._id,
      invoiceNumber: "INV-006",
      dateIssued: "2024-01-05",
      dueDate: "2024-02-04",
      totalAmount: 12000,
      status: "Overdue" as const
    },
    {
      customerId: xyzRetail._id,
      invoiceNumber: "INV-007",
      dateIssued: "2024-01-10",
      dueDate: "2024-02-09",
      totalAmount: 9500,
      status: "Overdue" as const
    }
  ];

  const createdInvoices = [];
  for (const invoice of invoices) {
    const id = await ctx.db.insert("invoices", invoice);
    createdInvoices.push({ id, ...invoice });
  }

  return { message: "Invoices created successfully", count: createdInvoices.length };
});

export const seedPayments = mutation(async (ctx) => {
  // Check if payments already exist
  const existingPayments = await ctx.db.query("payments").collect();
  if (existingPayments.length > 0) {
    return { message: "Payments already exist", count: existingPayments.length };
  }

  // Get invoices to reference
  const invoices = await ctx.db.query("invoices").collect();
  if (invoices.length === 0) {
    throw new Error("No invoices found. Please seed invoices first.");
  }

  const getInvoiceByNumber = (invoiceNumber: string) => invoices.find(inv => inv.invoiceNumber === invoiceNumber);

  const inv001 = getInvoiceByNumber("INV-001");
  const inv002 = getInvoiceByNumber("INV-002");

  if (!inv001 || !inv002) {
    throw new Error("Required invoices not found");
  }

  const payments = [
    {
      invoiceId: inv001._id,
      date: "2024-01-15",
      amount: 15000,
      method: "Bank Transfer" as const,
      reference: "TXN-001"
    },
    {
      invoiceId: inv002._id,
      date: "2024-02-15",
      amount: 25000,
      method: "Credit Card" as const,
      reference: "TXN-002"
    }
  ];

  const createdPayments = [];
  for (const payment of payments) {
    const id = await ctx.db.insert("payments", payment);
    createdPayments.push({ id, ...payment });
  }

  return { message: "Payments created successfully", count: createdPayments.length };
});

export const seedAllData = mutation(async (ctx) => {
  try {
    // Check if any data already exists
    const existingAccounts = await ctx.db.query("accounts").collect();
    const existingCustomers = await ctx.db.query("customers").collect();
    const existingJournalEntries = await ctx.db.query("journalEntries").collect();
    const existingInvoices = await ctx.db.query("invoices").collect();
    const existingPayments = await ctx.db.query("payments").collect();

    if (existingAccounts.length > 0 || existingCustomers.length > 0 ||
        existingJournalEntries.length > 0 || existingInvoices.length > 0 ||
        existingPayments.length > 0) {
      return {
        message: "Data already exists in the database",
        counts: {
          accounts: existingAccounts.length,
          customers: existingCustomers.length,
          journalEntries: existingJournalEntries.length,
          invoices: existingInvoices.length,
          payments: existingPayments.length
        }
      };
    }

    // Seed accounts first
    const accounts = [
      // Assets
      { name: "Cash", type: "Asset" as const, code: "1001" },
      { name: "Accounts Receivable", type: "Asset" as const, code: "1200" },
      { name: "Inventory", type: "Asset" as const, code: "1300" },
      { name: "Equipment", type: "Asset" as const, code: "1500" },
      { name: "Buildings", type: "Asset" as const, code: "1600" },

      // Liabilities
      { name: "Accounts Payable", type: "Liability" as const, code: "2001" },
      { name: "Notes Payable", type: "Liability" as const, code: "2100" },
      { name: "Accrued Expenses", type: "Liability" as const, code: "2200" },

      // Equity
      { name: "Owner's Equity", type: "Equity" as const, code: "3001" },
      { name: "Retained Earnings", type: "Equity" as const, code: "3200" },

      // Revenue
      { name: "Sales Revenue", type: "Revenue" as const, code: "4001" },
      { name: "Service Revenue", type: "Revenue" as const, code: "4100" },
      { name: "Interest Income", type: "Revenue" as const, code: "4200" },

      // Expenses
      { name: "Cost of Goods Sold", type: "Expense" as const, code: "5001" },
      { name: "Salaries Expense", type: "Expense" as const, code: "6001" },
      { name: "Rent Expense", type: "Expense" as const, code: "6100" },
      { name: "Utilities Expense", type: "Expense" as const, code: "6200" },
      { name: "Office Supplies", type: "Expense" as const, code: "6300" },
      { name: "Marketing Expense", type: "Expense" as const, code: "6400" },
      { name: "Insurance Expense", type: "Expense" as const, code: "6500" },
    ];

    const createdAccounts: Array<{ id: any; name: string; type: string; code: string }> = [];
    for (const account of accounts) {
      const id = await ctx.db.insert("accounts", account);
      createdAccounts.push({ id, ...account });
    }

    // Seed customers
    const customers = [
      {
        name: "ABC Manufacturing Corp",
        contactEmail: "<EMAIL>",
        phone: "******-0101",
        address: "123 Industrial Blvd, Manufacturing City, MC 12345"
      },
      {
        name: "XYZ Retail Solutions",
        contactEmail: "<EMAIL>",
        phone: "******-0102",
        address: "456 Commerce St, Retail Town, RT 67890"
      },
      {
        name: "Global Tech Industries",
        contactEmail: "<EMAIL>",
        phone: "******-0103",
        address: "789 Technology Ave, Tech City, TC 11111"
      },
      {
        name: "Local Services LLC",
        contactEmail: "<EMAIL>",
        phone: "******-0104",
        address: "321 Service Road, Service Village, SV 22222"
      },
      {
        name: "Premium Products Inc",
        contactEmail: "<EMAIL>",
        phone: "******-0105",
        address: "654 Premium Plaza, Luxury Lane, LL 33333"
      }
    ];

    const createdCustomers: Array<{ id: any; name: string; contactEmail: string; phone: string; address: string }> = [];
    for (const customer of customers) {
      const id = await ctx.db.insert("customers", customer);
      createdCustomers.push({ id, ...customer });
    }

    // Get account references for journal entries
    const getAccountByName = (name: string) => createdAccounts.find(acc => acc.name === name);
    const cashAccount = getAccountByName("Cash");
    const salesRevenueAccount = getAccountByName("Sales Revenue");
    const serviceRevenueAccount = getAccountByName("Service Revenue");
    const salariesExpenseAccount = getAccountByName("Salaries Expense");
    const rentExpenseAccount = getAccountByName("Rent Expense");
    const utilitiesExpenseAccount = getAccountByName("Utilities Expense");
    const marketingExpenseAccount = getAccountByName("Marketing Expense");
    const accountsReceivableAccount = getAccountByName("Accounts Receivable");

    // Create journal entries
    const journalEntries = [
      // Sales transactions
      { accountId: cashAccount!.id, date: "2024-01-15", description: "Cash sale to ABC Manufacturing", debit: 15000, credit: 0, reference: "INV-001" },
      { accountId: salesRevenueAccount!.id, date: "2024-01-15", description: "Cash sale to ABC Manufacturing", debit: 0, credit: 15000, reference: "INV-001" },

      { accountId: accountsReceivableAccount!.id, date: "2024-01-20", description: "Credit sale to XYZ Retail", debit: 25000, credit: 0, reference: "INV-002" },
      { accountId: salesRevenueAccount!.id, date: "2024-01-20", description: "Credit sale to XYZ Retail", debit: 0, credit: 25000, reference: "INV-002" },

      { accountId: cashAccount!.id, date: "2024-01-25", description: "Service revenue from Global Tech", debit: 8000, credit: 0, reference: "SRV-001" },
      { accountId: serviceRevenueAccount!.id, date: "2024-01-25", description: "Service revenue from Global Tech", debit: 0, credit: 8000, reference: "SRV-001" },

      // Expense transactions
      { accountId: salariesExpenseAccount!.id, date: "2024-01-31", description: "Monthly salaries", debit: 12000, credit: 0, reference: "PAY-001" },
      { accountId: cashAccount!.id, date: "2024-01-31", description: "Monthly salaries", debit: 0, credit: 12000, reference: "PAY-001" },

      { accountId: rentExpenseAccount!.id, date: "2024-02-01", description: "Office rent for February", debit: 3000, credit: 0, reference: "RENT-002" },
      { accountId: cashAccount!.id, date: "2024-02-01", description: "Office rent for February", debit: 0, credit: 3000, reference: "RENT-002" },

      { accountId: utilitiesExpenseAccount!.id, date: "2024-02-05", description: "Electricity bill", debit: 800, credit: 0, reference: "UTIL-001" },
      { accountId: cashAccount!.id, date: "2024-02-05", description: "Electricity bill", debit: 0, credit: 800, reference: "UTIL-001" },

      { accountId: marketingExpenseAccount!.id, date: "2024-02-10", description: "Online advertising", debit: 2500, credit: 0, reference: "MKT-001" },
      { accountId: cashAccount!.id, date: "2024-02-10", description: "Online advertising", debit: 0, credit: 2500, reference: "MKT-001" },

      // More recent transactions
      { accountId: cashAccount!.id, date: "2024-02-15", description: "Payment received from XYZ Retail", debit: 25000, credit: 0, reference: "PMT-001" },
      { accountId: accountsReceivableAccount!.id, date: "2024-02-15", description: "Payment received from XYZ Retail", debit: 0, credit: 25000, reference: "PMT-001" },

      { accountId: cashAccount!.id, date: "2024-02-20", description: "Cash sale to Local Services", debit: 18000, credit: 0, reference: "INV-003" },
      { accountId: salesRevenueAccount!.id, date: "2024-02-20", description: "Cash sale to Local Services", debit: 0, credit: 18000, reference: "INV-003" },
    ];

    const createdJournalEntries: Array<any> = [];
    for (const entry of journalEntries) {
      const id = await ctx.db.insert("journalEntries", entry);
      createdJournalEntries.push({ id, ...entry });
    }

    // Get customer references for invoices
    const getCustomerByName = (name: string) => createdCustomers.find(cust => cust.name === name);
    const abcManufacturing = getCustomerByName("ABC Manufacturing Corp");
    const xyzRetail = getCustomerByName("XYZ Retail Solutions");
    const globalTech = getCustomerByName("Global Tech Industries");
    const localServices = getCustomerByName("Local Services LLC");
    const premiumProducts = getCustomerByName("Premium Products Inc");

    // Create invoices
    const invoices = [
      {
        customerId: abcManufacturing!.id,
        invoiceNumber: "INV-001",
        dateIssued: "2024-01-15",
        dueDate: "2024-02-14",
        totalAmount: 15000,
        status: "Paid" as const
      },
      {
        customerId: xyzRetail!.id,
        invoiceNumber: "INV-002",
        dateIssued: "2024-01-20",
        dueDate: "2024-02-19",
        totalAmount: 25000,
        status: "Paid" as const
      },
      {
        customerId: globalTech!.id,
        invoiceNumber: "INV-003",
        dateIssued: "2024-01-25",
        dueDate: "2024-02-24",
        totalAmount: 8000,
        status: "Pending" as const
      },
      {
        customerId: localServices!.id,
        invoiceNumber: "INV-004",
        dateIssued: "2024-02-01",
        dueDate: "2024-03-03",
        totalAmount: 18000,
        status: "Pending" as const
      },
      {
        customerId: premiumProducts!.id,
        invoiceNumber: "INV-005",
        dateIssued: "2024-02-10",
        dueDate: "2024-03-12",
        totalAmount: 32000,
        status: "Pending" as const
      },
      {
        customerId: abcManufacturing!.id,
        invoiceNumber: "INV-006",
        dateIssued: "2024-01-05",
        dueDate: "2024-02-04",
        totalAmount: 12000,
        status: "Overdue" as const
      },
      {
        customerId: xyzRetail!.id,
        invoiceNumber: "INV-007",
        dateIssued: "2024-01-10",
        dueDate: "2024-02-09",
        totalAmount: 9500,
        status: "Overdue" as const
      }
    ];

    const createdInvoices: Array<any> = [];
    for (const invoice of invoices) {
      const id = await ctx.db.insert("invoices", invoice);
      createdInvoices.push({ id, ...invoice });
    }

    // Create payments for paid invoices
    const getInvoiceByNumber = (invoiceNumber: string) => createdInvoices.find(inv => inv.invoiceNumber === invoiceNumber);
    const inv001 = getInvoiceByNumber("INV-001");
    const inv002 = getInvoiceByNumber("INV-002");

    const payments = [
      {
        invoiceId: inv001!.id,
        date: "2024-01-15",
        amount: 15000,
        method: "Bank Transfer" as const,
        reference: "TXN-001"
      },
      {
        invoiceId: inv002!.id,
        date: "2024-02-15",
        amount: 25000,
        method: "Credit Card" as const,
        reference: "TXN-002"
      }
    ];

    const createdPayments: Array<any> = [];
    for (const payment of payments) {
      const id = await ctx.db.insert("payments", payment);
      createdPayments.push({ id, ...payment });
    }

    return {
      message: "Sample data created successfully",
      counts: {
        accounts: createdAccounts.length,
        customers: createdCustomers.length,
        journalEntries: createdJournalEntries.length,
        invoices: createdInvoices.length,
        payments: createdPayments.length
      }
    };
  } catch (error) {
    throw new Error(`Failed to seed data: ${error}`);
  }
});
