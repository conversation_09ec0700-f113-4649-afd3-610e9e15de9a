import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

// ============================================================================
// COMPREHENSIVE FINANCIAL MANAGEMENT SYSTEM
// Philippine GAAP Compliant • BIR Compliant • PFRS/PAS Standards
// ============================================================================

// 1. ACCOUNT BALANCE MANAGEMENT
// ============================================================================

// Add Opening Balance to Account (Cash, Capital, etc.)
export const addAccountBalance = mutation({
  args: {
    accountCode: v.string(),
    amount: v.number(),
    description: v.string(),
    date: v.string(),
    referenceNumber: v.string()
  },
  handler: async (ctx, args) => {
    const accounts = await ctx.db.query("accounts").collect();
    const account = accounts.find(acc => acc.code === args.accountCode);
    
    if (!account) {
      throw new Error(`Account with code ${args.accountCode} not found`);
    }

    // Get current fiscal period
    const currentPeriod = await ctx.db.query("fiscalPeriods")
      .filter(q => q.and(
        q.lte(q.field("startDate"), args.date),
        q.gte(q.field("endDate"), args.date),
        q.eq(q.field("status"), "Open")
      ))
      .first();

    if (!currentPeriod) {
      throw new Error("No open fiscal period found for the transaction date");
    }

    // Update account balance
    const currentBalance = account.currentBalance || 0;
    const newBalance = currentBalance + args.amount;
    
    await ctx.db.patch(account._id, {
      currentBalance: newBalance,
      openingBalance: account.openingBalance || args.amount
    });

    // Generate journal entry number
    const journalEntries = await ctx.db.query("journalEntries").collect();
    const entryNumber = `JE-${String(journalEntries.length + 1).padStart(6, '0')}`;

    // Determine offset account and create balanced journal entries
    let offsetAccountId = "";
    let debitAccountId = "";
    let creditAccountId = "";

    if (account.type === "Asset") {
      // Debit Asset, Credit Capital
      debitAccountId = account._id;
      const capitalAccount = accounts.find(acc => 
        acc.type === "Equity" && (acc.code === "300" || acc.name.toLowerCase().includes("capital"))
      );
      if (!capitalAccount) {
        throw new Error("Capital account not found. Please create account code 300 - Capital Stock");
      }
      creditAccountId = capitalAccount._id;
      offsetAccountId = capitalAccount._id;
      
      // Update capital account
      const capitalBalance = capitalAccount.currentBalance || 0;
      await ctx.db.patch(capitalAccount._id, {
        currentBalance: capitalBalance + args.amount,
        openingBalance: capitalAccount.openingBalance || args.amount
      });
    } else if (account.type === "Equity") {
      // Debit Cash, Credit Equity
      const cashAccount = accounts.find(acc => 
        acc.type === "Asset" && (acc.code === "111" || acc.name.toLowerCase().includes("cash"))
      );
      if (!cashAccount) {
        throw new Error("Cash account not found. Please create account code 111 - Cash");
      }
      debitAccountId = cashAccount._id;
      creditAccountId = account._id;
      offsetAccountId = cashAccount._id;
      
      // Update cash account
      const cashBalance = cashAccount.currentBalance || 0;
      await ctx.db.patch(cashAccount._id, {
        currentBalance: cashBalance + args.amount,
        openingBalance: cashAccount.openingBalance || args.amount
      });
    } else {
      throw new Error("Opening balances can only be added to Asset and Equity accounts");
    }

    // Create journal entries
    await ctx.db.insert("journalEntries", {
      entryNumber: entryNumber + "-1",
      date: args.date,
      accountId: debitAccountId,
      description: `${args.description} - ${args.referenceNumber}`,
      debit: args.amount,
      credit: 0,
      reference: args.referenceNumber,
      transactionType: "Opening Balance",
      fiscalPeriodId: currentPeriod._id,
      isPosted: true,
      postedBy: "System",
      postedDate: args.date,
      batchId: entryNumber
    });

    await ctx.db.insert("journalEntries", {
      entryNumber: entryNumber + "-2",
      date: args.date,
      accountId: creditAccountId,
      description: `${args.description} - ${args.referenceNumber}`,
      debit: 0,
      credit: args.amount,
      reference: args.referenceNumber,
      transactionType: "Opening Balance",
      fiscalPeriodId: currentPeriod._id,
      isPosted: true,
      postedBy: "System",
      postedDate: args.date,
      batchId: entryNumber
    });

    // Update account balances for the period
    await updateAccountBalance(ctx, debitAccountId, currentPeriod._id, args.amount, 0);
    await updateAccountBalance(ctx, creditAccountId, currentPeriod._id, 0, args.amount);

    return {
      success: true,
      message: `✅ Opening balance added successfully\n\n📋 Details:\n• Account: ${account.name} (${account.code})\n• Amount: ₱${args.amount.toLocaleString()}\n• New Balance: ₱${newBalance.toLocaleString()}\n• Journal Entry: ${entryNumber}`,
      accountName: account.name,
      accountCode: account.code,
      amount: args.amount,
      newBalance: newBalance,
      journalEntryNumber: entryNumber,
      fiscalPeriod: currentPeriod.periodName
    };
  }
});

// Set Multiple Opening Balances
export const setOpeningBalances = mutation({
  args: {
    balances: v.array(v.object({
      accountCode: v.string(),
      amount: v.number()
    })),
    date: v.string(),
    description: v.string(),
    referenceNumber: v.string()
  },
  handler: async (ctx, args) => {
    const accounts = await ctx.db.query("accounts").collect();
    const accountMap = new Map(accounts.map(acc => [acc.code, acc]));
    
    // Get current fiscal period
    const currentPeriod = await ctx.db.query("fiscalPeriods")
      .filter(q => q.and(
        q.lte(q.field("startDate"), args.date),
        q.gte(q.field("endDate"), args.date),
        q.eq(q.field("status"), "Open")
      ))
      .first();

    if (!currentPeriod) {
      throw new Error("No open fiscal period found for the transaction date");
    }

    let totalDebits = 0;
    let totalCredits = 0;
    const updatedAccounts = [];

    // Validate accounts and calculate totals
    for (const balance of args.balances) {
      const account = accountMap.get(balance.accountCode);
      if (!account) {
        throw new Error(`Account with code ${balance.accountCode} not found`);
      }
      
      // Assets and Expenses are debits, Liabilities/Equity/Revenue are credits
      if (account.normalBalance === "Debit") {
        totalDebits += balance.amount;
      } else {
        totalCredits += balance.amount;
      }
    }

    // Ensure debits equal credits
    if (Math.abs(totalDebits - totalCredits) > 0.01) {
      throw new Error(`Opening balances must be balanced. Debits: ₱${totalDebits.toLocaleString()}, Credits: ₱${totalCredits.toLocaleString()}`);
    }

    // Generate batch ID
    const journalEntries = await ctx.db.query("journalEntries").collect();
    const batchId = `OB-${String(journalEntries.length + 1).padStart(6, '0')}`;

    // Process each balance
    for (let i = 0; i < args.balances.length; i++) {
      const balance = args.balances[i];
      const account = accountMap.get(balance.accountCode);
      if (!account) continue;

      // Update account balance
      await ctx.db.patch(account._id, {
        currentBalance: balance.amount,
        openingBalance: balance.amount
      });

      // Create journal entry
      const isDebit = account.normalBalance === "Debit";
      const entryNumber = `${batchId}-${String(i + 1).padStart(3, '0')}`;
      
      await ctx.db.insert("journalEntries", {
        entryNumber: entryNumber,
        date: args.date,
        accountId: account._id,
        description: `${args.description} - ${account.name}`,
        debit: isDebit ? balance.amount : 0,
        credit: isDebit ? 0 : balance.amount,
        reference: args.referenceNumber,
        transactionType: "Opening Balance",
        fiscalPeriodId: currentPeriod._id,
        isPosted: true,
        postedBy: "System",
        postedDate: args.date,
        batchId: batchId
      });

      // Update account balance for the period
      await updateAccountBalance(
        ctx, 
        account._id, 
        currentPeriod._id, 
        isDebit ? balance.amount : 0, 
        isDebit ? 0 : balance.amount
      );

      updatedAccounts.push({
        accountName: account.name,
        accountCode: account.code,
        balance: balance.amount,
        type: account.type
      });
    }

    return {
      success: true,
      message: `✅ Opening balances set successfully\n\n📋 Summary:\n• Accounts Updated: ${updatedAccounts.length}\n• Total Debits: ₱${totalDebits.toLocaleString()}\n• Total Credits: ₱${totalCredits.toLocaleString()}\n• Batch ID: ${batchId}`,
      updatedAccounts,
      totalDebits,
      totalCredits,
      isBalanced: true,
      batchId,
      fiscalPeriod: currentPeriod.periodName
    };
  }
});

// Helper function to update account balances
async function updateAccountBalance(
  ctx: any, 
  accountId: string, 
  fiscalPeriodId: string, 
  debitAmount: number, 
  creditAmount: number
) {
  const existingBalance = await ctx.db.query("accountBalances")
    .filter(q => q.and(
      q.eq(q.field("accountId"), accountId),
      q.eq(q.field("fiscalPeriodId"), fiscalPeriodId)
    ))
    .first();

  if (existingBalance) {
    await ctx.db.patch(existingBalance._id, {
      totalDebits: existingBalance.totalDebits + debitAmount,
      totalCredits: existingBalance.totalCredits + creditAmount,
      closingBalance: existingBalance.openingBalance + 
        (existingBalance.totalDebits + debitAmount) - 
        (existingBalance.totalCredits + creditAmount),
      lastUpdated: new Date().toISOString()
    });
  } else {
    await ctx.db.insert("accountBalances", {
      accountId: accountId,
      fiscalPeriodId: fiscalPeriodId,
      openingBalance: 0,
      totalDebits: debitAmount,
      totalCredits: creditAmount,
      closingBalance: debitAmount - creditAmount,
      lastUpdated: new Date().toISOString()
    });
  }
}

// Get Account Balance
export const getAccountBalance = query({
  args: {
    accountCode: v.string(),
    asOfDate: v.optional(v.string())
  },
  handler: async (ctx, args) => {
    const accounts = await ctx.db.query("accounts").collect();
    const account = accounts.find(acc => acc.code === args.accountCode);
    
    if (!account) {
      throw new Error(`Account with code ${args.accountCode} not found`);
    }

    const asOfDate = args.asOfDate || new Date().toISOString().split('T')[0];

    // Calculate balance from journal entries
    const journalEntries = await ctx.db.query("journalEntries")
      .filter(q => q.and(
        q.eq(q.field("accountId"), account._id),
        q.lte(q.field("date"), asOfDate),
        q.eq(q.field("isPosted"), true)
      ))
      .collect();

    const calculatedBalance = journalEntries.reduce((balance, entry) => {
      return balance + entry.debit - entry.credit;
    }, 0);

    // Get fiscal period balance
    const currentPeriod = await ctx.db.query("fiscalPeriods")
      .filter(q => q.and(
        q.lte(q.field("startDate"), asOfDate),
        q.gte(q.field("endDate"), asOfDate)
      ))
      .first();

    let periodBalance = null;
    if (currentPeriod) {
      periodBalance = await ctx.db.query("accountBalances")
        .filter(q => q.and(
          q.eq(q.field("accountId"), account._id),
          q.eq(q.field("fiscalPeriodId"), currentPeriod._id)
        ))
        .first();
    }

    return {
      accountCode: account.code,
      accountName: account.name,
      accountType: account.type,
      normalBalance: account.normalBalance,
      storedBalance: account.currentBalance || 0,
      calculatedBalance,
      openingBalance: account.openingBalance || 0,
      isBalanced: Math.abs((account.currentBalance || 0) - calculatedBalance) < 0.01,
      transactionCount: journalEntries.length,
      asOfDate,
      periodBalance: periodBalance ? {
        openingBalance: periodBalance.openingBalance,
        totalDebits: periodBalance.totalDebits,
        totalCredits: periodBalance.totalCredits,
        closingBalance: periodBalance.closingBalance,
        fiscalPeriod: currentPeriod?.periodName
      } : null
    };
  }
});

// 2. SALES AND REVENUE TRACKING (PFRS 15 Compliant)
// ============================================================================

// Record Sales Transaction with PFRS 15 Revenue Recognition
export const recordSalesTransaction = mutation({
  args: {
    customerId: v.id("customers"),
    invoiceNumber: v.string(),
    invoiceDate: v.string(),
    dueDate: v.string(),
    lineItems: v.array(v.object({
      description: v.string(),
      quantity: v.number(),
      unitPrice: v.number(),
      amount: v.number(),
      vatType: v.union(v.literal("Vatable"), v.literal("VAT Exempt"), v.literal("Zero Rated"))
    })),
    vatableAmount: v.number(),
    vatExemptAmount: v.number(),
    zeroRatedAmount: v.number(),
    outputVAT: v.number(),
    totalAmount: v.number(),
    paymentTerms: v.string(),
    performanceObligations: v.array(v.object({
      description: v.string(),
      allocatedAmount: v.number(),
      deliveryDate: v.string(),
      satisfactionMethod: v.union(v.literal("Point in Time"), v.literal("Over Time"))
    })),
    departmentId: v.optional(v.string()),
    costCenterId: v.optional(v.string())
  },
  handler: async (ctx, args) => {
    // Get required accounts
    const accounts = await ctx.db.query("accounts").collect();
    const arAccount = accounts.find(acc => acc.code === "113");
    const contractLiabilityAccount = accounts.find(acc => acc.code === "202");
    const outputVATAccount = accounts.find(acc => acc.code === "201");

    if (!arAccount || !contractLiabilityAccount || !outputVATAccount) {
      throw new Error("Required accounts not found. Please ensure Chart of Accounts includes: 113-Accounts Receivable, 202-Contract Liability, 201-Output VAT");
    }

    // Get current fiscal period
    const currentPeriod = await ctx.db.query("fiscalPeriods")
      .filter(q => q.and(
        q.lte(q.field("startDate"), args.invoiceDate),
        q.gte(q.field("endDate"), args.invoiceDate),
        q.eq(q.field("status"), "Open")
      ))
      .first();

    if (!currentPeriod) {
      throw new Error("No open fiscal period found for the transaction date");
    }

    // Create customer invoice
    const invoiceId = await ctx.db.insert("customerInvoices", {
      customerId: args.customerId,
      invoiceNumber: args.invoiceNumber,
      invoiceDate: args.invoiceDate,
      dueDate: args.dueDate,
      totalAmount: args.totalAmount,
      paidAmount: 0,
      status: "Sent",
      salesOrderId: "",
      taxAmount: args.outputVAT,
      discountAmount: 0,
      // PFRS 15 Revenue Recognition
      revenueRecognitionStatus: "Not Recognized",
      revenueRecognizedAmount: 0,
      performanceObligationsFulfilled: false,
      // Philippine VAT Compliance
      vatableAmount: args.vatableAmount,
      vatExemptAmount: args.vatExemptAmount,
      zeroRatedAmount: args.zeroRatedAmount,
      outputVatAmount: args.outputVAT
    });

    // Create performance obligations
    for (const po of args.performanceObligations) {
      await ctx.db.insert("performanceObligations", {
        invoiceId: invoiceId,
        description: po.description,
        allocatedAmount: po.allocatedAmount,
        status: "Pending",
        satisfactionMethod: po.satisfactionMethod
      });
    }

    // Generate journal entry batch
    const journalEntries = await ctx.db.query("journalEntries").collect();
    const batchId = `SI-${args.invoiceNumber}`;
    const netAmount = args.totalAmount - args.outputVAT;

    // PFRS 15 Compliant Journal Entries
    // 1. Debit: Accounts Receivable
    await ctx.db.insert("journalEntries", {
      entryNumber: `${batchId}-001`,
      date: args.invoiceDate,
      accountId: arAccount._id,
      description: `Sales Invoice ${args.invoiceNumber}`,
      debit: args.totalAmount,
      credit: 0,
      reference: args.invoiceNumber,
      transactionType: "Sales",
      departmentId: args.departmentId || "",
      costCenterId: args.costCenterId || "",
      fiscalPeriodId: currentPeriod._id,
      isPosted: true,
      postedBy: "System",
      postedDate: args.invoiceDate,
      batchId: batchId
    });

    // 2. Credit: Contract Liability (PFRS 15 - Revenue deferred)
    await ctx.db.insert("journalEntries", {
      entryNumber: `${batchId}-002`,
      date: args.invoiceDate,
      accountId: contractLiabilityAccount._id,
      description: `Contract Liability - Invoice ${args.invoiceNumber} (PFRS 15)`,
      debit: 0,
      credit: netAmount,
      reference: args.invoiceNumber,
      transactionType: "Sales",
      departmentId: args.departmentId || "",
      costCenterId: args.costCenterId || "",
      fiscalPeriodId: currentPeriod._id,
      isPosted: true,
      postedBy: "System",
      postedDate: args.invoiceDate,
      batchId: batchId
    });

    // 3. Credit: Output VAT
    if (args.outputVAT > 0) {
      await ctx.db.insert("journalEntries", {
        entryNumber: `${batchId}-003`,
        date: args.invoiceDate,
        accountId: outputVATAccount._id,
        description: `Output VAT - Invoice ${args.invoiceNumber}`,
        debit: 0,
        credit: args.outputVAT,
        reference: args.invoiceNumber,
        transactionType: "Sales",
        departmentId: args.departmentId || "",
        costCenterId: args.costCenterId || "",
        fiscalPeriodId: currentPeriod._id,
        isPosted: true,
        postedBy: "System",
        postedDate: args.invoiceDate,
        batchId: batchId
      });
    }

    // Record VAT transaction for BIR compliance
    await ctx.db.insert("vatTransactions", {
      transactionDate: args.invoiceDate,
      transactionType: "Sales",
      documentType: "Sales Invoice",
      documentNumber: args.invoiceNumber,
      customerId: args.customerId,
      vatableAmount: args.vatableAmount,
      vatExemptAmount: args.vatExemptAmount,
      zeroRatedAmount: args.zeroRatedAmount,
      vatAmount: args.outputVAT,
      vatRate: 0.12,
      withholdingTaxAmount: 0,
      withholdingTaxRate: 0,
      netAmount: netAmount,
      totalAmount: args.totalAmount,
      fiscalPeriodId: currentPeriod._id
    });

    // Update account balances
    await updateAccountBalance(ctx, arAccount._id, currentPeriod._id, args.totalAmount, 0);
    await updateAccountBalance(ctx, contractLiabilityAccount._id, currentPeriod._id, 0, netAmount);
    if (args.outputVAT > 0) {
      await updateAccountBalance(ctx, outputVATAccount._id, currentPeriod._id, 0, args.outputVAT);
    }

    return {
      success: true,
      invoiceId,
      message: `✅ Sales transaction recorded successfully\n\n📋 PFRS 15 Compliance:\n• Revenue deferred until performance obligations satisfied\n• Contract Liability: ₱${netAmount.toLocaleString()}\n• Output VAT: ₱${args.outputVAT.toLocaleString()}\n• Batch ID: ${batchId}\n\n⚠️ Next Step: Recognize revenue when goods/services delivered`,
      totalAmount: args.totalAmount,
      contractLiability: netAmount,
      outputVAT: args.outputVAT,
      batchId,
      pfrs15Compliant: true
    };
  }
});
