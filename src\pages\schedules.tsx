import React from "react";
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import Sidebar from "../components/sidebar";
import Navbar from "../components/navbar";

function Schedules() {
  const arSchedule = useQuery(api.tasks.getAccountsReceivableSchedule);
  const apSchedule = useQuery(api.tasks.getAccountsPayableSchedule);

  if (!arSchedule || !apSchedule) {
    return (
      <div className="flex h-screen">
        <Sidebar />
        <div className="flex-1 flex flex-col">
          <Navbar />
          <main className="p-6 bg-gray-100 flex-1 overflow-auto">
            <h1 className="text-3xl font-bold mb-6">Account Schedules</h1>
            <p className="text-gray-500">Loading...</p>
          </main>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen">
      <Sidebar />
      <div className="flex-1 flex flex-col">
        <Navbar />
        <main className="p-6 bg-gray-100 flex-1 overflow-auto">
          <h1 className="text-3xl font-bold mb-6">Account Schedules</h1>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Accounts Receivable Schedule */}
            <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
              <h2 className="text-xl font-semibold mb-4 text-gray-800">
                Schedule of Accounts Receivable
              </h2>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="text-left py-3 px-4 font-semibold text-gray-700">Company</th>
                      <th className="text-right py-3 px-4 font-semibold text-gray-700">Amount</th>
                    </tr>
                  </thead>
                  <tbody>
                    {arSchedule.schedule.map((item, index) => (
                      <tr key={index} className="border-b border-gray-100 hover:bg-gray-50">
                        <td className="py-3 px-4">{item.company}</td>
                        <td className="py-3 px-4 text-right font-medium">
                          ₱{item.amount.toLocaleString()}
                        </td>
                      </tr>
                    ))}
                    <tr className="border-t-2 border-gray-300 font-semibold bg-gray-50">
                      <td className="py-3 px-4">Total</td>
                      <td className="py-3 px-4 text-right">
                        ₱{arSchedule.total.toLocaleString()}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            {/* Accounts Payable Schedule */}
            <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
              <h2 className="text-xl font-semibold mb-4 text-gray-800">
                Schedule of Accounts Payable
              </h2>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="text-left py-3 px-4 font-semibold text-gray-700">Company</th>
                      <th className="text-right py-3 px-4 font-semibold text-gray-700">Amount</th>
                    </tr>
                  </thead>
                  <tbody>
                    {apSchedule.schedule.map((item, index) => (
                      <tr key={index} className="border-b border-gray-100 hover:bg-gray-50">
                        <td className="py-3 px-4">{item.company}</td>
                        <td className="py-3 px-4 text-right font-medium">
                          ₱{item.amount.toLocaleString()}
                        </td>
                      </tr>
                    ))}
                    <tr className="border-t-2 border-gray-300 font-semibold bg-gray-50">
                      <td className="py-3 px-4">Total</td>
                      <td className="py-3 px-4 text-right">
                        ₱{apSchedule.total.toLocaleString()}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-blue-800 mb-2">
                Total Accounts Receivable
              </h3>
              <p className="text-3xl font-bold text-blue-600">
                ₱{arSchedule.total.toLocaleString()}
              </p>
              <p className="text-sm text-blue-600 mt-1">
                Amount owed by customers
              </p>
            </div>

            <div className="bg-red-50 border border-red-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-red-800 mb-2">
                Total Accounts Payable
              </h3>
              <p className="text-3xl font-bold text-red-600">
                ₱{apSchedule.total.toLocaleString()}
              </p>
              <p className="text-sm text-red-600 mt-1">
                Amount owed to suppliers
              </p>
            </div>
          </div>

          {/* Net Position */}
          <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6 mt-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">
              Net Working Capital Position
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center">
                <p className="text-sm text-gray-600">Accounts Receivable</p>
                <p className="text-xl font-semibold text-blue-600">
                  ₱{arSchedule.total.toLocaleString()}
                </p>
              </div>
              <div className="text-center">
                <p className="text-sm text-gray-600">Accounts Payable</p>
                <p className="text-xl font-semibold text-red-600">
                  ₱{apSchedule.total.toLocaleString()}
                </p>
              </div>
              <div className="text-center">
                <p className="text-sm text-gray-600">Net Position</p>
                <p className={`text-xl font-semibold ${
                  (arSchedule.total - apSchedule.total) >= 0 ? 'text-green-600' : 'text-red-600'
                }`}>
                  ₱{(arSchedule.total - apSchedule.total).toLocaleString()}
                </p>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}

export default Schedules;
