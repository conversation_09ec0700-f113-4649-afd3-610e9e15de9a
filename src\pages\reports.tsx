import { useState } from "react";
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import Sidebar from "../components/sidebar";
import Navbar from "../components/navbar";

type ReportType = 'overview' | 'income' | 'balance' | 'trial' | 'aging' | 'general' | 'journal';

function Reports() {
  const [activeReport, setActiveReport] = useState<ReportType>('overview');

  const incomeStatement = useQuery(api.tasks.getIncomeStatement);
  const balanceSheet = useQuery(api.tasks.getBalanceSheet);
  const trialBalance = useQuery(api.tasks.getTrialBalance);
  const agingReport = useQuery(api.tasks.getAgingReport);
  const journalEntries = useQuery(api.tasks.getJournalEntries);
  const generalLedger = useQuery(api.tasks.getGeneralLedger);

  const renderReportContent = () => {
    switch (activeReport) {
      case 'income':
        return <IncomeStatementReport data={incomeStatement} />;
      case 'balance':
        return <BalanceSheetReport data={balanceSheet} />;
      case 'trial':
        return <TrialBalanceReport data={trialBalance} />;
      case 'aging':
        return <AgingReport data={agingReport} />;
      case 'general':
        return <GeneralLedgerReport data={generalLedger} />;
      case 'journal':
        return <JournalEntriesReport data={journalEntries} />;
      default:
        return <ReportsOverview setActiveReport={setActiveReport} />;
    }
  };

  return (
    <div className="flex h-screen">
      <Sidebar />
      <div className="flex-1 flex flex-col">
        <Navbar />
        <main className="p-6 bg-gradient-to-l from-teal-50 to-gray-100 flex-1 overflow-auto">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-3xl font-bold">Financial Reports</h1>
            {activeReport !== 'overview' && (
              <button
                type="button"
                onClick={() => setActiveReport('overview')}
                className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 transition flex items-center"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Back to Overview
              </button>
            )}
          </div>

          {renderReportContent()}
        </main>
      </div>
    </div>
  );
}

function ReportsOverview({ setActiveReport }: { setActiveReport: (report: ReportType) => void }) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <ReportCard
        title="Income Statement"
        description="View revenue, expenses, and profit/loss over a period."
        icon={
          <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
        }
        onClick={() => setActiveReport('income')}
        color="bg-gray-50 border-teal-400 text-teal-600"
      />

      <ReportCard
        title="Balance Sheet"
        description="View assets, liabilities, and equity at a point in time."
        icon={
          <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16l-3-9m3 9l3-9" />
          </svg>
        }
        onClick={() => setActiveReport('balance')}
        color="bg-gray-50 border-teal-400 text-teal-600"
      />

      <ReportCard
        title="Trial Balance"
        description="View all account balances to ensure books balance."
        icon={
          <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
          </svg>
        }
        onClick={() => setActiveReport('trial')}
        color="bg-gray-50 border-teal-400 text-teal-600"
      />

      <ReportCard
        title="Aging Report"
        description="View outstanding receivables by age."
        icon={
          <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        }
        onClick={() => setActiveReport('aging')}
        color="bg-gray-50 border-teal-400 text-teal-600"
      />

      <ReportCard
        title="General Ledger"
        description="View detailed transaction history by account."
        icon={
          <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
          </svg>
        }
        onClick={() => setActiveReport('general')}
         color="bg-gray-50 border-teal-400 text-teal-600"
      />

      <ReportCard
        title="Journal Entries"
        description="View all journal entries and manual transactions."
        icon={
          <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
          </svg>
        }
        onClick={() => setActiveReport('journal')}
        color="bg-gray-50 border-teal-400 text-teal-600"
      />

      <ReportCard
        title="Cash Flow Statement"
        description="Track cash inflows and outflows over a period."
        icon={
          <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
          </svg>
        }
        onClick={() => {}}
         color="bg-gray-50 border-teal-400 text-teal-600"
        disabled={true}
      />
    </div>
  );
}

function ReportCard({
  title,
  description,
  icon,
  onClick,
  color,
  disabled = false
}: {
  title: string;
  description: string;
  icon: React.ReactNode;
  onClick: () => void;
  color: string;
  disabled?: boolean;
}) {
  return (
    <div className={`${color} border-2 rounded-xl shadow-lg p-6 transition-all ${disabled ? 'opacity-50 cursor-not-allowed' : 'hover:shadow-xl cursor-pointer'}`}>
      <div className="flex items-center mb-4">
        <div className="mr-4">
          {icon}
        </div>
        <h3 className="text-lg font-semibold">{title}</h3>
      </div>
      <p className="text-sm mb-4 opacity-80">
        {description}
      </p>
      <button
        type="button"
        onClick={onClick}
        disabled={disabled}
        className={`w-full px-4 py-2 rounded transition ${
          disabled
            ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
            : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
        }`}
      >
        {disabled ? 'Coming Soon' : 'View Report'}
      </button>
    </div>
  );
}

function IncomeStatementReport({ data }: { data: any }) {
  if (!data) {
    return <div className="bg-white rounded-xl shadow-lg p-8 text-center">Loading...</div>;
  }

  return (
    <div className="bg-white rounded-xl shadow-lg p-8">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-gray-800">Income Statement</h2>
        <p className="text-gray-600">For the Period Ending {new Date().toLocaleDateString()}</p>
      </div>

      <div className="space-y-6">
        {/* Revenue Section */}
        <div>
          <h3 className="text-lg font-semibold text-gray-800 mb-4 border-b border-gray-200 pb-2">Revenue</h3>
          <div className="space-y-2">
            {data.revenue.map((item: any, index: number) => (
              <div key={index} className="flex justify-between items-center py-2">
                <div>
                  <span className="text-gray-700">{item.accountName}</span>
                  <span className="text-gray-500 text-sm ml-2">({item.accountCode})</span>
                </div>
                <span className="font-medium text-green-600">${item.balance.toLocaleString()}</span>
              </div>
            ))}
            <div className="flex justify-between items-center py-2 border-t border-gray-200 font-semibold">
              <span className="text-gray-800">Total Revenue</span>
              <span className="text-green-600">${data.totalRevenue.toLocaleString()}</span>
            </div>
          </div>
        </div>

        {/* Expenses Section */}
        <div>
          <h3 className="text-lg font-semibold text-gray-800 mb-4 border-b border-gray-200 pb-2">Expenses</h3>
          <div className="space-y-2">
            {data.expenses.map((item: any, index: number) => (
              <div key={index} className="flex justify-between items-center py-2">
                <div>
                  <span className="text-gray-700">{item.accountName}</span>
                  <span className="text-gray-500 text-sm ml-2">({item.accountCode})</span>
                </div>
                <span className="font-medium text-red-600">${item.balance.toLocaleString()}</span>
              </div>
            ))}
            <div className="flex justify-between items-center py-2 border-t border-gray-200 font-semibold">
              <span className="text-gray-800">Total Expenses</span>
              <span className="text-red-600">${data.totalExpenses.toLocaleString()}</span>
            </div>
          </div>
        </div>

        {/* Net Income */}
        <div className="border-t-2 border-gray-300 pt-4">
          <div className="flex justify-between items-center py-3 bg-gray-50 rounded-lg px-4">
            <span className="text-xl font-bold text-gray-800">Net Income</span>
            <span className={`text-xl font-bold ${data.netIncome >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              ${data.netIncome.toLocaleString()}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}

function BalanceSheetReport({ data }: { data: any }) {
  if (!data) {
    return <div className="bg-white rounded-xl shadow-lg p-8 text-center">Loading...</div>;
  }

  return (
    <div className="bg-white rounded-xl shadow-lg p-8">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-gray-800">Balance Sheet</h2>
        <p className="text-gray-600">As of {new Date().toLocaleDateString()}</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Assets */}
        <div>
          <h3 className="text-lg font-semibold text-gray-800 mb-4 border-b border-gray-200 pb-2">Assets</h3>
          <div className="space-y-2">
            {data.assets.map((item: any, index: number) => (
              <div key={index} className="flex justify-between items-center py-2">
                <div>
                  <span className="text-gray-700">{item.accountName}</span>
                  <span className="text-gray-500 text-sm ml-2">({item.accountCode})</span>
                </div>
                <span className="font-medium text-blue-600">${item.balance.toLocaleString()}</span>
              </div>
            ))}
            <div className="flex justify-between items-center py-2 border-t border-gray-200 font-semibold">
              <span className="text-gray-800">Total Assets</span>
              <span className="text-blue-600">${data.totalAssets.toLocaleString()}</span>
            </div>
          </div>
        </div>

        {/* Liabilities & Equity */}
        <div>
          <h3 className="text-lg font-semibold text-gray-800 mb-4 border-b border-gray-200 pb-2">Liabilities</h3>
          <div className="space-y-2 mb-6">
            {data.liabilities.map((item: any, index: number) => (
              <div key={index} className="flex justify-between items-center py-2">
                <div>
                  <span className="text-gray-700">{item.accountName}</span>
                  <span className="text-gray-500 text-sm ml-2">({item.accountCode})</span>
                </div>
                <span className="font-medium text-orange-600">${item.balance.toLocaleString()}</span>
              </div>
            ))}
            <div className="flex justify-between items-center py-2 border-t border-gray-200 font-semibold">
              <span className="text-gray-800">Total Liabilities</span>
              <span className="text-orange-600">${data.totalLiabilities.toLocaleString()}</span>
            </div>
          </div>

          <h3 className="text-lg font-semibold text-gray-800 mb-4 border-b border-gray-200 pb-2">Equity</h3>
          <div className="space-y-2">
            {data.equity.map((item: any, index: number) => (
              <div key={index} className="flex justify-between items-center py-2">
                <div>
                  <span className="text-gray-700">{item.accountName}</span>
                  <span className="text-gray-500 text-sm ml-2">({item.accountCode})</span>
                </div>
                <span className="font-medium text-green-600">${item.balance.toLocaleString()}</span>
              </div>
            ))}
            <div className="flex justify-between items-center py-2 border-t border-gray-200 font-semibold">
              <span className="text-gray-800">Total Equity</span>
              <span className="text-green-600">${data.totalEquity.toLocaleString()}</span>
            </div>
          </div>

          <div className="mt-6 pt-4 border-t-2 border-gray-300">
            <div className="flex justify-between items-center py-3 bg-gray-50 rounded-lg px-4">
              <span className="font-bold text-gray-800">Total Liabilities & Equity</span>
              <span className="font-bold text-gray-800">${(data.totalLiabilities + data.totalEquity).toLocaleString()}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

function TrialBalanceReport({ data }: { data: any }) {
  if (!data || !data.summary) {
    return <div className="bg-white rounded-xl shadow-lg p-8 text-center">Loading...</div>;
  }

  const { accounts, summary } = data;

  return (
    <div className="bg-white rounded-xl shadow-lg p-8">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-gray-800">Trial Balance</h2>
        <p className="text-gray-600">As of {new Date().toLocaleDateString()}</p>
        <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium mt-2 ${
          summary.isBalanced ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
        }`}>
          {summary.isBalanced ? '✓ Books are balanced' : '⚠ Books are not balanced'}
        </div>
      </div>

      {accounts && accounts.length > 0 ? (
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b-2 border-gray-300">
                <th className="text-left py-3 px-4 font-semibold text-gray-700">Account Code</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-700">Account Name</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-700">Type</th>
                <th className="text-right py-3 px-4 font-semibold text-gray-700">Debit Balance</th>
                <th className="text-right py-3 px-4 font-semibold text-gray-700">Credit Balance</th>
              </tr>
            </thead>
            <tbody>
              {accounts.map((account: any, index: number) => (
                <tr key={index} className="border-b border-gray-100 hover:bg-gray-50">
                  <td className="py-3 px-4 text-sm font-medium text-gray-900">{account.accountCode}</td>
                  <td className="py-3 px-4 text-sm text-gray-900">{account.accountName}</td>
                  <td className="py-3 px-4 text-sm">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      account.accountType === 'Asset' ? 'bg-blue-100 text-blue-800' :
                      account.accountType === 'Liability' ? 'bg-red-100 text-red-800' :
                      account.accountType === 'Equity' ? 'bg-purple-100 text-purple-800' :
                      account.accountType === 'Revenue' ? 'bg-green-100 text-green-800' :
                      'bg-orange-100 text-orange-800'
                    }`}>
                      {account.accountType}
                    </span>
                  </td>
                  <td className="py-3 px-4 text-sm text-right">
                    {account.balance > 0 && (account.accountType === 'Asset' || account.accountType === 'Expense') ? (
                      <span className="text-red-600 font-medium">${account.balance.toLocaleString()}</span>
                    ) : (
                      <span className="text-gray-400">—</span>
                    )}
                  </td>
                  <td className="py-3 px-4 text-sm text-right">
                    {account.balance > 0 && (account.accountType === 'Liability' || account.accountType === 'Equity' || account.accountType === 'Revenue') ? (
                      <span className="text-green-600 font-medium">${account.balance.toLocaleString()}</span>
                    ) : (
                      <span className="text-gray-400">—</span>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
            <tfoot>
              <tr className="border-t-2 border-gray-300 bg-gray-50">
                <td colSpan={3} className="py-3 px-4 font-bold text-gray-800">Totals</td>
                <td className="py-3 px-4 text-right font-bold text-red-600">${summary.totalDebits.toLocaleString()}</td>
                <td className="py-3 px-4 text-right font-bold text-green-600">${summary.totalCredits.toLocaleString()}</td>
              </tr>
            </tfoot>
          </table>
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="text-gray-400 text-lg mb-4">No account activity found</div>
          <p className="text-gray-600">
            Create accounts and record transactions to see your trial balance.
          </p>
        </div>
      )}
    </div>
  );
}

function AgingReport({ data }: { data: any }) {
  if (!data) {
    return <div className="bg-white rounded-xl shadow-lg p-8 text-center">Loading...</div>;
  }

  return (
    <div className="bg-white rounded-xl shadow-lg p-8">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-gray-800">Accounts Receivable Aging Report</h2>
        <p className="text-gray-600">As of {new Date().toLocaleDateString()}</p>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-8">
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
          <div className="text-lg font-bold text-green-600">${data.summary.current.toLocaleString()}</div>
          <div className="text-sm text-gray-600">Current</div>
        </div>
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-center">
          <div className="text-lg font-bold text-yellow-600">${data.summary.days1to30.toLocaleString()}</div>
          <div className="text-sm text-gray-600">1-30 Days</div>
        </div>
        <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 text-center">
          <div className="text-lg font-bold text-orange-600">${data.summary.days31to60.toLocaleString()}</div>
          <div className="text-sm text-gray-600">31-60 Days</div>
        </div>
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-center">
          <div className="text-lg font-bold text-red-600">${data.summary.days61to90.toLocaleString()}</div>
          <div className="text-sm text-gray-600">61-90 Days</div>
        </div>
        <div className="bg-red-100 border border-red-300 rounded-lg p-4 text-center">
          <div className="text-lg font-bold text-red-700">${data.summary.over90Days.toLocaleString()}</div>
          <div className="text-sm text-gray-600">Over 90 Days</div>
        </div>
      </div>

      <div className="mb-6">
        <div className="flex justify-between items-center py-3 bg-gray-50 rounded-lg px-4">
          <span className="font-bold text-gray-800">Total Outstanding</span>
          <span className="font-bold text-gray-800">${data.totalOutstanding.toLocaleString()}</span>
        </div>
      </div>

      {/* Detailed Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b-2 border-gray-300">
              <th className="text-left py-3 px-4 font-semibold text-gray-700">Customer</th>
              <th className="text-left py-3 px-4 font-semibold text-gray-700">Invoice #</th>
              <th className="text-left py-3 px-4 font-semibold text-gray-700">Date Issued</th>
              <th className="text-left py-3 px-4 font-semibold text-gray-700">Due Date</th>
              <th className="text-right py-3 px-4 font-semibold text-gray-700">Amount</th>
              <th className="text-center py-3 px-4 font-semibold text-gray-700">Days Past Due</th>
              <th className="text-center py-3 px-4 font-semibold text-gray-700">Status</th>
            </tr>
          </thead>
          <tbody>
            {data.invoices.map((invoice: any, index: number) => (
              <tr key={index} className="border-b border-gray-100 hover:bg-gray-50">
                <td className="py-3 px-4 text-sm font-medium text-gray-900">{invoice.customerName}</td>
                <td className="py-3 px-4 text-sm text-gray-900">{invoice.invoiceNumber}</td>
                <td className="py-3 px-4 text-sm text-gray-600">{new Date(invoice.dateIssued).toLocaleDateString()}</td>
                <td className="py-3 px-4 text-sm text-gray-600">{new Date(invoice.dueDate).toLocaleDateString()}</td>
                <td className="py-3 px-4 text-sm text-right font-medium">${invoice.amount.toLocaleString()}</td>
                <td className="py-3 px-4 text-sm text-center">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    invoice.daysPastDue <= 0 ? 'bg-green-100 text-green-800' :
                    invoice.daysPastDue <= 30 ? 'bg-yellow-100 text-yellow-800' :
                    invoice.daysPastDue <= 60 ? 'bg-orange-100 text-orange-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                    {invoice.daysPastDue > 0 ? `${invoice.daysPastDue} days` : 'Current'}
                  </span>
                </td>
                <td className="py-3 px-4 text-sm text-center">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    invoice.status === 'Pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'
                  }`}>
                    {invoice.status}
                  </span>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}

function GeneralLedgerReport({ data }: { data: any }) {
  const [selectedAccountId, setSelectedAccountId] = useState<string>("");

  if (!data) {
    return <div className="bg-white rounded-xl shadow-lg p-8 text-center">Loading...</div>;
  }

  const selectedLedger = selectedAccountId
    ? data.find((ledger: any) => ledger.account._id === selectedAccountId)
    : null;

  return (
    <div className="space-y-6">
      <div className="bg-white rounded-xl shadow-lg p-8">
        <div className="text-center mb-8">
          <h2 className="text-2xl font-bold text-gray-800">General Ledger</h2>
          <p className="text-gray-600">As of {new Date().toLocaleDateString()}</p>
        </div>

        {/* Account Selector */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Select Account to View Ledger
          </label>
          <select
            value={selectedAccountId}
            onChange={(e) => setSelectedAccountId(e.target.value)}
            className="w-full max-w-md px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">Choose an account...</option>
            {data.map((ledger: any) => (
              <option key={ledger.account._id} value={ledger.account._id}>
                {ledger.account.code} - {ledger.account.name}
              </option>
            ))}
          </select>
        </div>

        {!selectedLedger ? (
          /* Account Summary Cards */
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {data.map((ledger: any) => (
              <div
                key={ledger.account._id}
                className="bg-gray-50 rounded-lg border border-gray-200 p-6 cursor-pointer hover:shadow-lg transition-shadow"
                onClick={() => setSelectedAccountId(ledger.account._id)}
              >
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">{ledger.account.name}</h3>
                    <p className="text-sm text-gray-500">{ledger.account.code}</p>
                  </div>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    ledger.account.type === 'Asset' ? 'bg-blue-100 text-blue-800' :
                    ledger.account.type === 'Liability' ? 'bg-red-100 text-red-800' :
                    ledger.account.type === 'Equity' ? 'bg-purple-100 text-purple-800' :
                    ledger.account.type === 'Revenue' ? 'bg-green-100 text-green-800' :
                    'bg-orange-100 text-orange-800'
                  }`}>
                    {ledger.account.type}
                  </span>
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Entries:</span>
                    <span className="text-sm font-medium">{ledger.entries.length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Balance:</span>
                    <span className={`text-sm font-bold ${
                      ledger.finalBalance >= 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      ${Math.abs(ledger.finalBalance).toLocaleString()}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          /* Selected Account Ledger */
          <div className="space-y-6">
            {/* Account Header */}
            <div className="bg-gray-50 rounded-lg p-6">
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="text-xl font-bold text-gray-900">{selectedLedger.account.name}</h3>
                  <p className="text-gray-600">Account Code: {selectedLedger.account.code}</p>
                  <p className="text-gray-600">Account Type: {selectedLedger.account.type}</p>
                </div>
                <div className="text-right">
                  <div className="text-sm text-gray-600">Current Balance</div>
                  <div className={`text-2xl font-bold ${
                    selectedLedger.finalBalance >= 0 ? 'text-green-600' : 'text-red-600'
                  }`}>
                    ${Math.abs(selectedLedger.finalBalance).toLocaleString()}
                  </div>
                </div>
              </div>
            </div>

            {/* Ledger Entries */}
            <div>
              <div className="flex justify-between items-center mb-4">
                <h4 className="text-lg font-semibold">Transaction History</h4>
                <button
                  type="button"
                  onClick={() => setSelectedAccountId("")}
                  className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                >
                  ← Back to All Accounts
                </button>
              </div>

              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b-2 border-gray-300">
                      <th className="text-left py-3 px-4 font-semibold text-gray-700">Date</th>
                      <th className="text-left py-3 px-4 font-semibold text-gray-700">Description</th>
                      <th className="text-left py-3 px-4 font-semibold text-gray-700">Reference</th>
                      <th className="text-right py-3 px-4 font-semibold text-gray-700">Debit</th>
                      <th className="text-right py-3 px-4 font-semibold text-gray-700">Credit</th>
                      <th className="text-right py-3 px-4 font-semibold text-gray-700">Balance</th>
                    </tr>
                  </thead>
                  <tbody>
                    {selectedLedger.entries.map((entry: any, index: number) => (
                      <tr key={index} className="border-b border-gray-100 hover:bg-gray-50">
                        <td className="py-3 px-4 text-sm">
                          {new Date(entry.date).toLocaleDateString()}
                        </td>
                        <td className="py-3 px-4 text-sm">{entry.description}</td>
                        <td className="py-3 px-4 text-sm font-mono">{entry.reference || '—'}</td>
                        <td className="py-3 px-4 text-right font-mono">
                          {entry.debit > 0 ? (
                            <span className="text-red-600">${entry.debit.toLocaleString()}</span>
                          ) : (
                            <span className="text-gray-400">—</span>
                          )}
                        </td>
                        <td className="py-3 px-4 text-right font-mono">
                          {entry.credit > 0 ? (
                            <span className="text-green-600">${entry.credit.toLocaleString()}</span>
                          ) : (
                            <span className="text-gray-400">—</span>
                          )}
                        </td>
                        <td className="py-3 px-4 text-right font-mono font-medium">
                          <span className={entry.runningBalance >= 0 ? 'text-green-600' : 'text-red-600'}>
                            ${Math.abs(entry.runningBalance).toLocaleString()}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

function JournalEntriesReport({ data }: { data: any }) {
  if (!data) {
    return <div className="bg-white rounded-xl shadow-lg p-8 text-center">Loading...</div>;
  }

  return (
    <div className="bg-white rounded-xl shadow-lg p-8">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-gray-800">Journal Entries</h2>
        <p className="text-gray-600">All recorded transactions</p>
      </div>

      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b-2 border-gray-300">
              <th className="text-left py-3 px-4 font-semibold text-gray-700">Date</th>
              <th className="text-left py-3 px-4 font-semibold text-gray-700">Description</th>
              <th className="text-left py-3 px-4 font-semibold text-gray-700">Reference</th>
              <th className="text-left py-3 px-4 font-semibold text-gray-700">Type</th>
              <th className="text-right py-3 px-4 font-semibold text-gray-700">Debit</th>
              <th className="text-right py-3 px-4 font-semibold text-gray-700">Credit</th>
            </tr>
          </thead>
          <tbody>
            {data.map((entry: any, index: number) => (
              <tr key={index} className="border-b border-gray-100 hover:bg-gray-50">
                <td className="py-3 px-4 text-sm">
                  {new Date(entry.date).toLocaleDateString()}
                </td>
                <td className="py-3 px-4 text-sm">{entry.description}</td>
                <td className="py-3 px-4 text-sm font-mono">{entry.reference || '—'}</td>
                <td className="py-3 px-4 text-sm">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    entry.transactionType === 'Sales' ? 'bg-green-100 text-green-800' :
                    entry.transactionType === 'Purchase' ? 'bg-blue-100 text-blue-800' :
                    entry.transactionType === 'Production' ? 'bg-purple-100 text-purple-800' :
                    entry.transactionType === 'Payroll' ? 'bg-orange-100 text-orange-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {entry.transactionType || 'General'}
                  </span>
                </td>
                <td className="py-3 px-4 text-right font-mono">
                  {entry.debit > 0 ? (
                    <span className="text-red-600 font-medium">${entry.debit.toLocaleString()}</span>
                  ) : (
                    <span className="text-gray-400">—</span>
                  )}
                </td>
                <td className="py-3 px-4 text-right font-mono">
                  {entry.credit > 0 ? (
                    <span className="text-green-600 font-medium">${entry.credit.toLocaleString()}</span>
                  ) : (
                    <span className="text-gray-400">—</span>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {data.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-400 text-lg mb-4">No journal entries found</div>
          <p className="text-gray-600">
            Journal entries will appear here when transactions are recorded.
          </p>
        </div>
      )}
    </div>
  );
}

export default Reports;
