import React, { useState } from "react";
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import Sidebar from "../components/sidebar";
import Navbar from "../components/navbar";
import FinancialCharts from "../components/charts/FinancialCharts";
import ManufacturingCharts from "../components/charts/ManufacturingCharts";

function Dashboard() {
  const [activeView, setActiveView] = useState<'overview' | 'financial-charts' | 'manufacturing-charts'>('overview');
  const data = useQuery(api.tasks.getDashboardData);

  if (!data) {
    return (
      <div className="flex h-screen bg-gradient-to-br from-slate-50 to-blue-50">
        <Sidebar />
        <div className="flex-1 flex flex-col">
          <Navbar />
          <main className="p-8 flex-1 overflow-auto">
            <div className="max-w-7xl mx-auto">
              <div className="text-center py-16">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-6">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>
                <h1 className="text-3xl font-bold text-gray-900 mb-4">XYZ Manufacturing ERP Dashboard</h1>
                <p className="text-lg text-gray-600 mb-2">Loading your financial data...</p>
                <p className="text-sm text-gray-500">Please wait while we gather the latest information</p>
              </div>
            </div>
          </main>
        </div>
      </div>
    );
  }

  const {
    revenue,
    expenses,
    profit,
    totalAssets,
    totalLiabilities,
    netWorth,
    totalInvoices,
    paidInvoices,
    pendingInvoices,
    overdueInvoices,
    outstandingInvoiceAmount,
    totalPayments,
    totalCustomers,
    recentTransactions,
    accountsSummary
  } = data;

  // Check if database is empty
  const isDatabaseEmpty = revenue === 0 && expenses === 0 && totalInvoices === 0;

  return (
    <div className="flex h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      <Sidebar />
      <div className="flex-1 flex flex-col">
        <Navbar />
        <main className="p-8 flex-1 overflow-auto">
          <div className="max-w-7xl mx-auto">
            {/* Header Section */}
            <div className="mb-8">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h1 className="text-4xl font-bold text-gray-900 mb-2">
                    XYZ Manufacturing ERP
                  </h1>
                  <p className="text-lg text-gray-600">
                    Financial Dashboard & Business Intelligence
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-sm text-gray-500">Last Updated</p>
                  <p className="text-lg font-semibold text-gray-700">
                    {new Date().toLocaleDateString('en-US', {
                      weekday: 'long',
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </p>
                </div>
              </div>

              {/* View Navigation Buttons */}
              <div className="flex flex-wrap gap-3 mb-6">
                <button
                  type="button"
                  onClick={() => setActiveView('overview')}
                  className={`inline-flex items-center px-6 py-3 rounded-xl font-semibold transition-all duration-200 shadow-sm ${
                    activeView === 'overview'
                      ? 'bg-blue-600 text-white shadow-lg shadow-blue-200 transform scale-105'
                      : 'bg-white text-gray-700 border border-gray-200 hover:bg-blue-50 hover:border-blue-300 hover:text-blue-700'
                  }`}
                >
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                  Overview
                </button>
                <button
                  type="button"
                  onClick={() => setActiveView('financial-charts')}
                  className={`inline-flex items-center px-6 py-3 rounded-xl font-semibold transition-all duration-200 shadow-sm ${
                    activeView === 'financial-charts'
                      ? 'bg-blue-600 text-white shadow-lg shadow-blue-200 transform scale-105'
                      : 'bg-white text-gray-700 border border-gray-200 hover:bg-blue-50 hover:border-blue-300 hover:text-blue-700'
                  }`}
                >
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                  Financial Charts
                </button>
                <button
                  type="button"
                  onClick={() => setActiveView('manufacturing-charts')}
                  className={`inline-flex items-center px-6 py-3 rounded-xl font-semibold transition-all duration-200 shadow-sm ${
                    activeView === 'manufacturing-charts'
                      ? 'bg-blue-600 text-white shadow-lg shadow-blue-200 transform scale-105'
                      : 'bg-white text-gray-700 border border-gray-200 hover:bg-blue-50 hover:border-blue-300 hover:text-blue-700'
                  }`}
                >
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                  </svg>
                  Manufacturing
                </button>
          </div>

            {/* Data Entry Section - Only show when database is empty */}
            {isDatabaseEmpty && (
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl shadow-xl border border-blue-200 p-8 mb-8">
                <div className="text-center mb-8">
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4">
                    <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                  </div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">Welcome to XYZ Manufacturing ERP</h2>
                  <p className="text-gray-600 max-w-2xl mx-auto">
                    Get started by setting up your basic data. Click on any section below to begin configuring your ERP system.
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  <a
                    href="/chart-of-accounts"
                    className="group flex flex-col items-center p-6 bg-white rounded-xl border-2 border-blue-200 hover:border-blue-400 hover:shadow-lg transition-all duration-200 transform hover:-translate-y-1"
                  >
                    <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-200">
                      <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                      </svg>
                    </div>
                    <div className="text-center">
                      <div className="font-bold text-gray-900 mb-1">Chart of Accounts</div>
                      <div className="text-sm text-gray-600">Set up your financial accounts structure</div>
                    </div>
                  </a>

                  <a
                    href="/customers"
                    className="group flex flex-col items-center p-6 bg-white rounded-xl border-2 border-green-200 hover:border-green-400 hover:shadow-lg transition-all duration-200 transform hover:-translate-y-1"
                  >
                    <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-200">
                      <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                      </svg>
                    </div>
                    <div className="text-center">
                      <div className="font-bold text-gray-900 mb-1">Customers</div>
                      <div className="text-sm text-gray-600">Manage customer relationships</div>
                    </div>
                  </a>

                  <a
                    href="/suppliers"
                    className="group flex flex-col items-center p-6 bg-white rounded-xl border-2 border-purple-200 hover:border-purple-400 hover:shadow-lg transition-all duration-200 transform hover:-translate-y-1"
                  >
                    <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-200">
                      <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                      </svg>
                    </div>
                    <div className="text-center">
                      <div className="font-bold text-gray-900 mb-1">Suppliers</div>
                      <div className="text-sm text-gray-600">Vendor & supplier management</div>
                    </div>
                  </a>

                  <a
                    href="/journal-entries"
                    className="group flex flex-col items-center p-6 bg-white rounded-xl border-2 border-orange-200 hover:border-orange-400 hover:shadow-lg transition-all duration-200 transform hover:-translate-y-1"
                  >
                    <div className="w-16 h-16 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-200">
                      <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                      </svg>
                    </div>
                    <div className="text-center">
                      <div className="font-bold text-gray-900 mb-1">Journal Entries</div>
                      <div className="text-sm text-gray-600">Record financial transactions</div>
                    </div>
                  </a>
                </div>
              </div>
            )}

            {/* Always show dashboard content and charts */}
            <>
              {activeView === 'financial-charts' && <FinancialCharts data={data} />}
              {activeView === 'manufacturing-charts' && <ManufacturingCharts data={data} />}
              {activeView === 'overview' && (
                <>
                  {/* Key Performance Indicators */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <EnhancedCard
                      title="Total Revenue"
                      value={revenue}
                      color="text-blue-600"
                      bgColor="bg-gradient-to-br from-blue-50 to-blue-100"
                      borderColor="border-blue-200"
                      icon={
                        <svg className="w-7 h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                        </svg>
                      }
                      trend="+12.5%"
                      trendColor="text-green-600"
                    />
                    <EnhancedCard
                      title="Total Expenses"
                      value={expenses}
                      color="text-orange-600"
                      bgColor="bg-gradient-to-br from-orange-50 to-orange-100"
                      borderColor="border-orange-200"
                      icon={
                        <svg className="w-7 h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6" />
                        </svg>
                      }
                      trend="+8.2%"
                      trendColor="text-orange-600"
                    />
                    <EnhancedCard
                      title="Net Profit"
                      value={profit}
                      color={profit >= 0 ? "text-green-600" : "text-red-600"}
                      bgColor={profit >= 0 ? "bg-gradient-to-br from-green-50 to-green-100" : "bg-gradient-to-br from-red-50 to-red-100"}
                      borderColor={profit >= 0 ? "border-green-200" : "border-red-200"}
                      icon={
                        <svg className="w-7 h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                        </svg>
                      }
                      trend={profit >= 0 ? "+15.3%" : "-5.2%"}
                      trendColor={profit >= 0 ? "text-green-600" : "text-red-600"}
                    />
                    <EnhancedCard
                      title="Net Worth"
                      value={netWorth}
                      color={netWorth >= 0 ? "text-indigo-600" : "text-red-600"}
                      bgColor={netWorth >= 0 ? "bg-gradient-to-br from-indigo-50 to-indigo-100" : "bg-gradient-to-br from-red-50 to-red-100"}
                      borderColor={netWorth >= 0 ? "border-indigo-200" : "border-red-200"}
                      icon={
                        <svg className="w-7 h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                        </svg>
                      }
                      trend={netWorth >= 0 ? "+22.1%" : "-12.8%"}
                      trendColor={netWorth >= 0 ? "text-green-600" : "text-red-600"}
                    />
              </div>

                  {/* Financial Position */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                    <div className="bg-white rounded-2xl shadow-xl border border-gray-200 p-8 hover:shadow-2xl transition-all duration-200">
                      <h3 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
                        <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mr-4">
                          <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                          </svg>
                        </div>
                        Financial Position
                      </h3>
                      <div className="space-y-4">
                        <div className="flex justify-between items-center p-4 bg-gradient-to-r from-blue-50 to-blue-100 border-l-4 border-blue-500 rounded-lg">
                          <div>
                            <span className="text-blue-700 font-semibold text-lg">Total Assets</span>
                            <p className="text-blue-600 text-sm">Current + Fixed Assets</p>
                          </div>
                          <span className="text-blue-700 font-bold text-2xl">${totalAssets.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between items-center p-4 bg-gradient-to-r from-orange-50 to-orange-100 border-l-4 border-orange-500 rounded-lg">
                          <div>
                            <span className="text-orange-700 font-semibold text-lg">Total Liabilities</span>
                            <p className="text-orange-600 text-sm">Current + Long-term</p>
                          </div>
                          <span className="text-orange-700 font-bold text-2xl">${totalLiabilities.toLocaleString()}</span>
                        </div>
                        <div className={`flex justify-between items-center p-4 bg-gradient-to-r ${netWorth >= 0 ? 'from-green-50 to-green-100 border-l-4 border-green-500' : 'from-red-50 to-red-100 border-l-4 border-red-500'} rounded-lg`}>
                          <div>
                            <span className={`font-semibold text-lg ${netWorth >= 0 ? 'text-green-700' : 'text-red-700'}`}>Net Equity</span>
                            <p className={`text-sm ${netWorth >= 0 ? 'text-green-600' : 'text-red-600'}`}>Assets - Liabilities</p>
                          </div>
                          <span className={`font-bold text-2xl ${netWorth >= 0 ? 'text-green-700' : 'text-red-700'}`}>
                            ${netWorth.toLocaleString()}
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="bg-white rounded-2xl shadow-xl border border-gray-200 p-8 hover:shadow-2xl transition-all duration-200">
                      <h3 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
                        <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center mr-4">
                          <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                          </svg>
                        </div>
                        Invoice Overview
                      </h3>
                      <div className="grid grid-cols-2 gap-4 mb-6">
                        <div className="text-center p-4 bg-gradient-to-br from-green-50 to-green-100 rounded-xl border border-green-200">
                          <div className="text-3xl font-bold text-green-600 mb-1">{paidInvoices}</div>
                          <div className="text-sm font-medium text-green-700">Paid Invoices</div>
                          <div className="text-xs text-green-600 mt-1">✓ Completed</div>
                        </div>
                        <div className="text-center p-4 bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-xl border border-yellow-200">
                          <div className="text-3xl font-bold text-yellow-600 mb-1">{pendingInvoices}</div>
                          <div className="text-sm font-medium text-yellow-700">Pending</div>
                          <div className="text-xs text-yellow-600 mt-1">⏳ In Progress</div>
                        </div>
                        <div className="text-center p-4 bg-gradient-to-br from-red-50 to-red-100 rounded-xl border border-red-200">
                          <div className="text-3xl font-bold text-red-600 mb-1">{overdueInvoices}</div>
                          <div className="text-sm font-medium text-red-700">Overdue</div>
                          <div className="text-xs text-red-600 mt-1">⚠️ Action Required</div>
                        </div>
                        <div className="text-center p-4 bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl border border-blue-200">
                          <div className="text-3xl font-bold text-blue-600 mb-1">{totalInvoices}</div>
                          <div className="text-sm font-medium text-blue-700">Total</div>
                          <div className="text-xs text-blue-600 mt-1">📊 All Invoices</div>
                        </div>
                      </div>
                      <div className="p-4 bg-gradient-to-r from-orange-50 to-orange-100 border-l-4 border-orange-500 rounded-lg">
                        <div className="flex justify-between items-center">
                          <div>
                            <span className="text-orange-700 font-semibold">Outstanding Amount</span>
                            <p className="text-orange-600 text-sm">Total unpaid invoices</p>
                          </div>
                          <span className="font-bold text-orange-700 text-xl">${outstandingInvoiceAmount.toLocaleString()}</span>
                        </div>
                      </div>
                    </div>
              </div>

              

                  {/* Recent Transactions */}
                  {recentTransactions.length > 0 && (
                    <div className="bg-white rounded-2xl shadow-xl border border-gray-200 p-8 hover:shadow-2xl transition-all duration-200">
                      <h3 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
                        <div className="w-12 h-12 bg-gradient-to-br from-gray-500 to-gray-600 rounded-xl flex items-center justify-center mr-4">
                          <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                        </div>
                        Recent Transactions
                        <span className="ml-auto text-sm font-normal text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
                          Last {recentTransactions.length} entries
                        </span>
                      </h3>
                      <div className="overflow-x-auto bg-gray-50 rounded-xl">
                        <table className="w-full">
                          <thead>
                            <tr className="bg-gradient-to-r from-gray-100 to-gray-200 border-b-2 border-gray-300">
                              <th className="text-left py-4 px-6 font-bold text-gray-800 text-sm uppercase tracking-wider">Date</th>
                              <th className="text-left py-4 px-6 font-bold text-gray-800 text-sm uppercase tracking-wider">Account</th>
                              <th className="text-left py-4 px-6 font-bold text-gray-800 text-sm uppercase tracking-wider">Description</th>
                              <th className="text-right py-4 px-6 font-bold text-gray-800 text-sm uppercase tracking-wider">Debit</th>
                              <th className="text-right py-4 px-6 font-bold text-gray-800 text-sm uppercase tracking-wider">Credit</th>
                              <th className="text-center py-4 px-6 font-bold text-gray-800 text-sm uppercase tracking-wider">Reference</th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {recentTransactions.map((transaction, index) => (
                              <tr key={index} className="hover:bg-blue-50 transition-all duration-200 group">
                                <td className="py-5 px-6">
                                  <div className="flex items-center">
                                    <div className="w-2 h-2 bg-blue-500 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity"></div>
                                    <div className="text-sm font-semibold text-gray-900">
                                      {new Date(transaction.date).toLocaleDateString('en-US', {
                                        month: 'short',
                                        day: 'numeric',
                                        year: 'numeric'
                                      })}
                                    </div>
                                  </div>
                                </td>
                                <td className="py-5 px-6">
                                  <div>
                                    <div className="text-sm font-semibold text-gray-900">{transaction.accountName}</div>
                                    <div className="text-xs font-medium text-blue-600 bg-blue-100 px-2 py-1 rounded-full inline-block mt-1">
                                      {transaction.accountCode}
                                    </div>
                                  </div>
                                </td>
                                <td className="py-5 px-6">
                                  <div className="text-sm text-gray-900 max-w-xs truncate" title={transaction.description}>
                                    {transaction.description}
                                  </div>
                                </td>
                                <td className="py-5 px-6 text-right">
                                  {transaction.debit > 0 ? (
                                    <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold bg-red-100 text-red-800">
                                      ${transaction.debit.toLocaleString()}
                                    </span>
                                  ) : (
                                    <span className="text-gray-400 text-lg">—</span>
                                  )}
                                </td>
                                <td className="py-5 px-6 text-right">
                                  {transaction.credit > 0 ? (
                                    <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold bg-green-100 text-green-800">
                                      ${transaction.credit.toLocaleString()}
                                    </span>
                                  ) : (
                                    <span className="text-gray-400 text-lg">—</span>
                                  )}
                                </td>
                                <td className="py-5 px-6 text-center">
                                  <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-gray-100 text-gray-700 border border-gray-200">
                                    {transaction.reference || 'N/A'}
                                  </span>
                                </td>
                              </tr>
                            ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}
                </>
              )}
            </>
          </div>
        </main>
      </div>
    </div>
  );
}

function EnhancedCard({
  title,
  value,
  color,
  bgColor,
  borderColor,
  icon,
  trend,
  trendColor
}: {
  title: string;
  value: number;
  color: string;
  bgColor: string;
  borderColor: string;
  icon: React.ReactNode;
  trend?: string;
  trendColor?: string;
}) {
  return (
    <div className={`${bgColor} ${borderColor} border-2 rounded-2xl shadow-lg p-6 transition-all duration-200 hover:shadow-xl hover:-translate-y-1`}>
      <div className="flex items-center justify-between mb-4">
        <div className={`p-3 rounded-xl ${color} bg-white bg-opacity-20`}>
          {icon}
        </div>
        {trend && (
          <div className={`text-sm font-semibold ${trendColor} flex items-center`}>
            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
            </svg>
            {trend}
          </div>
        )}
      </div>
      <div>
        <p className="text-sm font-medium text-gray-600 mb-2">{title}</p>
        <p className={`text-3xl font-bold ${color}`}>
          ${Number(value).toLocaleString()}
        </p>
      </div>
    </div>
  );
}

function MetricCard({
  title,
  value,
  icon,
  color,
  bgColor
}: {
  title: string;
  value: number | string;
  icon: React.ReactNode;
  color: string;
  bgColor: string;
}) {
  return (
    <div className={`${bgColor} rounded-xl shadow-lg p-6 border border-gray-200 transition-all hover:shadow-xl`}>
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
          <p className={`text-2xl font-bold ${color}`}>
            {typeof value === 'number' ? value.toLocaleString() : value}
          </p>
        </div>
        <div className={`${color} opacity-80`}>
          {icon}
        </div>
      </div>
    </div>
  );
}

export default Dashboard;
