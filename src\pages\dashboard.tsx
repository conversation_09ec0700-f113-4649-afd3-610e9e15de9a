import React, { useState } from "react";
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import Sidebar from "../components/sidebar";
import Navbar from "../components/navbar";
import FinancialCharts from "../components/charts/FinancialCharts";
import ManufacturingCharts from "../components/charts/ManufacturingCharts";

function Dashboard() {
  const [activeView, setActiveView] = useState<'overview' | 'financial-charts' | 'manufacturing-charts'>('overview');
  const data = useQuery(api.tasks.getDashboardData);

  if (!data) {
    return (
      <div className="flex h-screen">
        <Sidebar />
        <div className="flex-1 flex flex-col">
          <Navbar />
          <main className="p-6 bg-gray-100 flex-1 overflow-auto">
            <h1 className="text-3xl font-bold mb-6">Finance Dashboard</h1>
            <p className="text-gray-500">Loading data...</p>
          </main>
        </div>
      </div>
    );
  }

  const {
    revenue,
    expenses,
    profit,
    totalAssets,
    totalLiabilities,
    netWorth,
    totalInvoices,
    paidInvoices,
    pendingInvoices,
    overdueInvoices,
    outstandingInvoiceAmount,
    totalPayments,
    totalCustomers,
    recentTransactions,
    accountsSummary
  } = data;

  // Check if database is empty
  const isDatabaseEmpty = revenue === 0 && expenses === 0 && totalInvoices === 0;

  return (
    <div className="flex h-screen">
      <Sidebar />
      <div className="flex-1 flex flex-col">
        <Navbar />
      <main className="p-6 bg-gradient-to-l from-teal-50 to-gray-50flex-1 overflow-auto">

          

          {/* View Navigation Buttons */}
          <div className="flex space-x-4 mb-6">
            <button
              type="button"
              onClick={() => setActiveView('overview')}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                activeView === 'overview'
                  ? 'bg-gradient from-teal-50 to-gray-50 text-teal-400 border-2 border-teal-300'
                  : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
              }`}
            >
              Overview
            </button>
            <button
              type="button"
              onClick={() => setActiveView('financial-charts')}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                activeView === 'financial-charts'
                  ? 'bg-gradient from-teal-50 to-gray-50 text-teal-400 border-2 border-teal-300'
                  : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
              }`}
            >
              Financial Charts
            </button>
            <button
              type="button"
              onClick={() => setActiveView('manufacturing-charts')}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                activeView === 'manufacturing-charts'
                  ? 'bg-gradient from-teal-50 to-gray-50 text-teal-400 border-2 border-teal-300'
                  : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
              }`}
            >
              Manufacturing Charts
            </button>
          </div>

          {/* Data Entry Section - Only show when database is empty */}
          {isDatabaseEmpty && (
            <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6 mb-6">
              
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <a
                  href="/chart-of-accounts"
                  className="flex items-center p-4 bg-gradient-to-bl from-blue-50 to-gray-50 rounded-lg border-2 border-blue-200 hover:bg-blue-100 transition-colors"
                >
                  <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                    <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                  </div>
                  <div>
                    <div className="font-medium text-blue-800">Chart of Accounts</div>
                    <div className="text-sm text-blue-600">Set up your accounts</div>
                  </div>
                </a>

                <a
                  href="/customers"
                  className="flex items-center p-4  bg-gradient-to-bl from-green-50 to-gray-50 rounded-lg border border-green-200 hover:bg-green-100 transition-colors"
                >
                  <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                    <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                  </div>
                  <div>
                    <div className="font-medium text-green-800">Customers</div>
                    <div className="text-sm text-green-600">Add customer data</div>
                  </div>
                </a>

                <a
                  href="/suppliers"
                  className="flex items-center p-4  bg-gradient-to-bl from-purple-50 to-gray-50 rounded-lg border border-purple-200 hover:bg-purple-100 transition-colors"
                >
                  <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                    <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                  </div>
                  <div>
                    <div className="font-medium text-purple-800">Suppliers</div>
                    <div className="text-sm text-purple-600">Manage suppliers</div>
                  </div>
                </a>

                <a
                  href="/journal-entries"
                  className="flex items-center p-4  bg-gradient-to-bl from-orange-50 to-gray-50 rounded-lg border-2 border-orange-200 hover:bg-orange-100 transition-colors"
                >
                  <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
                    <svg className="w-5 h-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                  </div>
                  <div>
                    <div className="font-medium text-orange-800">Journal Entries</div>
                    <div className="text-sm text-orange-600">Record transactions</div>
                  </div>
                </a>
              </div>
            </div>
          )}

          {/* Always show dashboard content and charts */}
          <>
            {activeView === 'financial-charts' && <FinancialCharts data={data} />}
            {activeView === 'manufacturing-charts' && <ManufacturingCharts data={data} />}
            {activeView === 'overview' && (
              <>
                {/* Key Performance Indicators */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <EnhancedCard
                  title="Total Revenue"
                  value={revenue}
                  color="text-blue-300"
                  bgColor=" bg-gradient-to-bl from-blue-50 to-gray-50"
                  borderColor="border-blue-200"
                  icon={
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                    </svg>
                  }
                />
                <EnhancedCard
                  title="Total Expenses"
                  value={expenses}
                  color="text-orange-300"
                  bgColor=" bg-gradient-to-bl from-orange-50 to-gray-50"
                  borderColor="border-orange-200"
                  icon={
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6" />
                    </svg>
                  }
                />
                <EnhancedCard
                  title="Net Profit"
                  value={profit}
                  color={profit >= 0 ? "text-green-400" : "text-red-500"}
                  bgColor={profit >= 0 ? "bg-gradient-to-bl  from-green-50 to-gray-50" : " bg-gradient-to-bl from-red-50 to-gray-50"}
                  borderColor={profit >= 0 ? "border-green-300" : "border-red-200"}
                  icon={
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                    </svg>
                  }
                />
                <EnhancedCard
                  title="Net Worth"
                  value={netWorth}
                  color={netWorth >= 0 ? "text-yellow-300" : "text-red-600"}
                  bgColor={netWorth >= 0 ? "bg-gradient-to-bl from-yellow-50 to-gray-50" : "bg-gradient-to-bl from-red-50 to-gray-50"}
                  borderColor={netWorth >= 0 ? "border-yellow-200" : "border-red-200"}
                  icon={
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                  }
                />
              </div>

              {/* Financial Position */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8 ">
                <div className="bg-white rounded-xl  shadow-lg shadow-gray-100 border border-teal-300 p-6">
                  <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                    <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                      <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                      </svg>
                    </div>
                    Assets & Liabilities
                  </h3>
                  <div className="space-y-4 ">
                    <div className="flex justify-between items-center p-3  border border-blue-200 text-lg rounded-lg">
                      <span className="text-blue-400 font-medium">Total Assets</span>
                      <span className="text-blue-400 font-bold ">${totalAssets.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between items-center p-3 border border-orange-200 text-lg rounded-lg">
                      <span className="text-orange-400 font-medium">Total Liabilities</span>
                      <span className="text-orange-400 font-bold text-lg">${totalLiabilities.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between items-center p-3  border text-lg rounded-lg border- border-yellow-200">
                      <span className={`font-medium ${netWorth >= 0 ? 'text-yellow-600' : 'text-red-600'}`}>Net Equity</span>
                      <span className={`font-bold text-lg ${netWorth >= 0 ? 'text-yellow-600' : 'text-red-600'}`}>
                        ${netWorth.toLocaleString()}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="bg-white rounded-xl shadow-lg shadow-gray-200 border border-teal-300  p-6">
                  <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                    <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                      <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                    </div>
                    Invoice Summary
                  </h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-3 bg-green-50 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">{paidInvoices}</div>
                      <div className="text-sm text-gray-600">Paid</div>
                    </div>
                    <div className="text-center p-3 bg-yellow-50 rounded-lg">
                      <div className="text-2xl font-bold text-yellow-600">{pendingInvoices}</div>
                      <div className="text-sm text-gray-600">Pending</div>
                    </div>
                    <div className="text-center p-3 bg-red-50 rounded-lg">
                      <div className="text-2xl font-bold text-red-600">{overdueInvoices}</div>
                      <div className="text-sm text-gray-600">Overdue</div>
                    </div>
                    <div className="text-center p-3 bg-blue-50 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">{totalInvoices}</div>
                      <div className="text-sm text-gray-600">Total</div>
                    </div>
                  </div>
                  <div className="mt-4 pt-4 border-t border-gray-200">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Outstanding Amount:</span>
                      <span className="font-semibold text-orange-600">${outstandingInvoiceAmount.toLocaleString()}</span>
                    </div>
                  </div>
                </div>
              </div>

              

              {/* Recent Transactions */}
              {recentTransactions.length > 0 && (
                <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
                  <h3 className="text-lg font-semibold text-gray-800 mb-6 flex items-center">
                    <div className="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center mr-3">
                      <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    Recent Transactions
                  </h3>
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b border-gray-200">
                          <th className="text-left py-3 px-4 font-semibold text-gray-700">Date</th>
                          <th className="text-left py-3 px-4 font-semibold text-gray-700">Account</th>
                          <th className="text-left py-3 px-4 font-semibold text-gray-700">Description</th>
                          <th className="text-right py-3 px-4 font-semibold text-gray-700">Debit</th>
                          <th className="text-right py-3 px-4 font-semibold text-gray-700">Credit</th>
                          <th className="text-center py-3 px-4 font-semibold text-gray-700">Reference</th>
                        </tr>
                      </thead>
                      <tbody>
                        {recentTransactions.map((transaction, index) => (
                          <tr key={index} className="border-b border-gray-100 hover:bg-gray-50 transition-colors">
                            <td className="py-4 px-4">
                              <div className="text-sm font-medium text-gray-900">
                                {new Date(transaction.date).toLocaleDateString()}
                              </div>
                            </td>
                            <td className="py-4 px-4">
                              <div>
                                <div className="text-sm font-medium text-gray-900">{transaction.accountName}</div>
                                <div className="text-xs text-gray-500">{transaction.accountCode}</div>
                              </div>
                            </td>
                            <td className="py-4 px-4">
                              <div className="text-sm text-gray-900">{transaction.description}</div>
                            </td>
                            <td className="py-4 px-4 text-right">
                              {transaction.debit > 0 ? (
                                <span className="text-red-600 font-medium">${transaction.debit.toLocaleString()}</span>
                              ) : (
                                <span className="text-gray-400">—</span>
                              )}
                            </td>
                            <td className="py-4 px-4 text-right">
                              {transaction.credit > 0 ? (
                                <span className="text-green-600 font-medium">${transaction.credit.toLocaleString()}</span>
                              ) : (
                                <span className="text-gray-400">—</span>
                              )}
                            </td>
                            <td className="py-4 px-4 text-center">
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                {transaction.reference || 'N/A'}
                              </span>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}
              </>
            )}
          </>
        </main>
      </div>
    </div>
  );
}

function EnhancedCard({
  title,
  value,
  color,
  bgColor,
  borderColor,
  icon
}: {
  title: string;
  value: number;
  color: string;
  bgColor: string;
  borderColor: string;
  icon: React.ReactNode;
}) {
  return (
    <div className={`${bgColor} ${borderColor} border-2 rounded-xl shadow-lg p-6 transition-all hover:shadow-xl`}>
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
          <p className={`text-3xl font-bold ${color}`}>
            ${Number(value).toLocaleString()}
          </p>
        </div>
        <div className={`${color} opacity-80`}>
          {icon}
        </div>
      </div>
    </div>
  );
}

function MetricCard({
  title,
  value,
  icon,
  color,
  bgColor
}: {
  title: string;
  value: number | string;
  icon: React.ReactNode;
  color: string;
  bgColor: string;
}) {
  return (
    <div className={`${bgColor} rounded-xl shadow-lg p-6 border border-gray-200 transition-all hover:shadow-xl`}>
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
          <p className={`text-2xl font-bold ${color}`}>
            {typeof value === 'number' ? value.toLocaleString() : value}
          </p>
        </div>
        <div className={`${color} opacity-80`}>
          {icon}
        </div>
      </div>
    </div>
  );
}

export default Dashboard;
