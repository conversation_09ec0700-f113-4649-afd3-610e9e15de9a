import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

// CORE FINANCIAL FUNCTIONS - Philippine GAAP Compliant
// Focus: Revenue Tracking, Expense Management, Financial Reporting

// ============================================================================
// REVENUE TRACKING - PFRS 15 Compliant
// ============================================================================

// Record Sales Revenue with proper PFRS 15 compliance
export const recordSalesRevenue = mutation({
  args: {
    customerId: v.id("customers"),
    invoiceNumber: v.string(),
    invoiceDate: v.string(),
    dueDate: v.string(),
    lineItems: v.array(v.object({
      description: v.string(),
      quantity: v.number(),
      unitPrice: v.number(),
      amount: v.number(),
      vatType: v.union(v.literal("Vatable"), v.literal("VAT Exempt"), v.literal("Zero Rated"))
    })),
    vatableAmount: v.number(),
    vatExemptAmount: v.number(),
    zeroRatedAmount: v.number(),
    outputVAT: v.number(), // 12% VAT
    totalAmount: v.number(),
    paymentTerms: v.string(),
    performanceObligations: v.array(v.object({
      description: v.string(),
      allocatedAmount: v.number(),
      deliveryDate: v.string(),
      satisfactionMethod: v.union(v.literal("Point in Time"), v.literal("Over Time"))
    }))
  },
  handler: async (ctx, args) => {
    // Get required accounts
    const accounts = await ctx.db.query("accounts").collect();
    const arAccount = accounts.find(acc => acc.code === "113" || acc.name.includes("Accounts Receivable"));
    const contractLiabilityAccount = accounts.find(acc => acc.code === "202" || acc.name.includes("Contract Liability"));
    const outputVATAccount = accounts.find(acc => acc.code === "201" || acc.name.includes("Output Tax"));

    if (!arAccount || !contractLiabilityAccount || !outputVATAccount) {
      throw new Error("Required accounts not found. Please ensure Chart of Accounts is properly set up.");
    }

    // Create customer invoice
    const invoiceId = await ctx.db.insert("customerInvoices", {
      customerId: args.customerId,
      invoiceNumber: args.invoiceNumber,
      invoiceDate: args.invoiceDate,
      dueDate: args.dueDate,
      totalAmount: args.totalAmount,
      paidAmount: 0,
      status: "Sent",
      salesOrderId: "",
      taxAmount: args.outputVAT,
      discountAmount: 0,
      // PFRS 15 Revenue Recognition fields
      revenueRecognitionStatus: "Not Recognized",
      revenueRecognizedAmount: 0,
      performanceObligationsFulfilled: false,
      // Philippine VAT Compliance
      vatableAmount: args.vatableAmount,
      vatExemptAmount: args.vatExemptAmount,
      zeroRatedAmount: args.zeroRatedAmount,
      outputVatAmount: args.outputVAT
    });

    // Create performance obligations for PFRS 15 compliance
    for (const po of args.performanceObligations) {
      await ctx.db.insert("performanceObligations", {
        invoiceId: invoiceId,
        description: po.description,
        allocatedAmount: po.allocatedAmount,
        status: "Pending",
        satisfactionMethod: po.satisfactionMethod
      });
    }

    // PFRS 15 Compliant Journal Entries
    const netAmount = args.totalAmount - args.outputVAT;

    // 1. Debit: Accounts Receivable
    await ctx.db.insert("journalEntries", {
      accountId: arAccount._id,
      date: args.invoiceDate,
      description: `Sales Invoice ${args.invoiceNumber}`,
      debit: args.totalAmount,
      credit: 0,
      reference: args.invoiceNumber,
      transactionType: "Sales",
      departmentId: "",
      costCenterId: ""
    });

    // 2. Credit: Contract Liability (PFRS 15 - Revenue not yet recognized)
    await ctx.db.insert("journalEntries", {
      accountId: contractLiabilityAccount._id,
      date: args.invoiceDate,
      description: `Contract Liability - Invoice ${args.invoiceNumber} (PFRS 15)`,
      debit: 0,
      credit: netAmount,
      reference: args.invoiceNumber,
      transactionType: "Sales",
      departmentId: "",
      costCenterId: ""
    });

    // 3. Credit: Output VAT
    if (args.outputVAT > 0) {
      await ctx.db.insert("journalEntries", {
        accountId: outputVATAccount._id,
        date: args.invoiceDate,
        description: `Output VAT - Invoice ${args.invoiceNumber}`,
        debit: 0,
        credit: args.outputVAT,
        reference: args.invoiceNumber,
        transactionType: "Sales",
        departmentId: "",
        costCenterId: ""
      });
    }

    // Record VAT transaction for BIR compliance
    await ctx.db.insert("vatTransactions", {
      transactionDate: args.invoiceDate,
      transactionType: "Sales",
      documentType: "Sales Invoice",
      documentNumber: args.invoiceNumber,
      customerId: args.customerId,
      vatableAmount: args.vatableAmount,
      vatExemptAmount: args.vatExemptAmount,
      zeroRatedAmount: args.zeroRatedAmount,
      vatAmount: args.outputVAT,
      vatRate: 0.12, // 12% Philippine VAT
      withholdingTaxAmount: 0,
      withholdingTaxRate: 0,
      netAmount: netAmount,
      totalAmount: args.totalAmount
    });

    return {
      success: true,
      invoiceId,
      message: `✅ Sales Invoice ${args.invoiceNumber} recorded successfully\n\n📋 PFRS 15 Compliance:\n• Revenue deferred until performance obligations satisfied\n• Contract Liability: ₱${netAmount.toLocaleString()}\n• Output VAT: ₱${args.outputVAT.toLocaleString()}\n\n⚠️ Next Step: Recognize revenue when goods/services delivered`,
      totalAmount: args.totalAmount,
      contractLiability: netAmount,
      outputVAT: args.outputVAT,
      pfrs15Compliant: true
    };
  }
});

// Recognize Revenue when Performance Obligations are satisfied (PFRS 15)
export const recognizeRevenue = mutation({
  args: {
    invoiceId: v.id("customerInvoices"),
    performanceObligationId: v.id("performanceObligations"),
    recognitionDate: v.string(),
    recognitionAmount: v.number(),
    deliveryConfirmation: v.string(),
    controlTransferred: v.boolean()
  },
  handler: async (ctx, args) => {
    if (!args.controlTransferred) {
      throw new Error("Revenue can only be recognized when control has transferred to the customer per PFRS 15");
    }

    const invoice = await ctx.db.get(args.invoiceId);
    const performanceObligation = await ctx.db.get(args.performanceObligationId);
    
    if (!invoice || !performanceObligation) {
      throw new Error("Invoice or Performance Obligation not found");
    }

    // Get required accounts
    const accounts = await ctx.db.query("accounts").collect();
    const revenueAccount = accounts.find(acc => acc.code === "400" || acc.name.includes("Sales Revenue"));
    const contractLiabilityAccount = accounts.find(acc => acc.code === "202" || acc.name.includes("Contract Liability"));

    if (!revenueAccount || !contractLiabilityAccount) {
      throw new Error("Required revenue accounts not found");
    }

    // Update performance obligation
    await ctx.db.patch(args.performanceObligationId, {
      status: "Satisfied",
      satisfactionDate: args.recognitionDate
    });

    // Revenue Recognition Journal Entries (PFRS 15)
    // 1. Debit: Contract Liability
    await ctx.db.insert("journalEntries", {
      accountId: contractLiabilityAccount._id,
      date: args.recognitionDate,
      description: `Revenue Recognition - Invoice ${invoice.invoiceNumber} (PFRS 15)`,
      debit: args.recognitionAmount,
      credit: 0,
      reference: invoice.invoiceNumber,
      transactionType: "Revenue Recognition",
      departmentId: "",
      costCenterId: ""
    });

    // 2. Credit: Sales Revenue
    await ctx.db.insert("journalEntries", {
      accountId: revenueAccount._id,
      date: args.recognitionDate,
      description: `Sales Revenue - Invoice ${invoice.invoiceNumber}`,
      debit: 0,
      credit: args.recognitionAmount,
      reference: invoice.invoiceNumber,
      transactionType: "Revenue Recognition",
      departmentId: "",
      costCenterId: ""
    });

    // Update invoice revenue recognition status
    const newRecognizedAmount = (invoice.revenueRecognizedAmount || 0) + args.recognitionAmount;
    const netAmount = invoice.totalAmount - invoice.taxAmount;
    const isFullyRecognized = newRecognizedAmount >= netAmount;

    await ctx.db.patch(args.invoiceId, {
      revenueRecognizedAmount: newRecognizedAmount,
      revenueRecognitionStatus: isFullyRecognized ? "Fully Recognized" : "Partially Recognized",
      performanceObligationsFulfilled: isFullyRecognized,
      controlTransferredDate: args.recognitionDate
    });

    return {
      success: true,
      message: `✅ Revenue recognized successfully\n\n📊 PFRS 15 Details:\n• Amount Recognized: ₱${args.recognitionAmount.toLocaleString()}\n• Total Recognized: ₱${newRecognizedAmount.toLocaleString()}\n• Remaining: ₱${(netAmount - newRecognizedAmount).toLocaleString()}\n• Status: ${isFullyRecognized ? 'Fully Recognized' : 'Partially Recognized'}`,
      recognizedAmount: args.recognitionAmount,
      totalRecognized: newRecognizedAmount,
      remainingToRecognize: netAmount - newRecognizedAmount,
      status: isFullyRecognized ? "Fully Recognized" : "Partially Recognized"
    };
  }
});

// Record Customer Payment (Accounts Receivable)
export const recordCustomerPayment = mutation({
  args: {
    invoiceId: v.id("customerInvoices"),
    paymentDate: v.string(),
    paymentAmount: v.number(),
    paymentMethod: v.union(v.literal("Cash"), v.literal("Check"), v.literal("Bank Transfer"), v.literal("Credit Card")),
    referenceNumber: v.string(),
    bankAccount: v.optional(v.string()),
    withholdingTax: v.optional(v.number()) // For customers who withhold tax
  },
  handler: async (ctx, args) => {
    const invoice = await ctx.db.get(args.invoiceId);
    if (!invoice) {
      throw new Error("Invoice not found");
    }

    const remainingBalance = invoice.totalAmount - invoice.paidAmount;
    if (args.paymentAmount > remainingBalance) {
      throw new Error(`Payment amount (₱${args.paymentAmount.toLocaleString()}) exceeds remaining balance (₱${remainingBalance.toLocaleString()})`);
    }

    // Get required accounts
    const accounts = await ctx.db.query("accounts").collect();
    const cashAccount = accounts.find(acc => acc.code === "111" || acc.name.includes("Cash"));
    const arAccount = accounts.find(acc => acc.code === "113" || acc.name.includes("Accounts Receivable"));
    const withholdingTaxAccount = accounts.find(acc => acc.code === "119" || acc.name.includes("Creditable Withholding Tax"));

    if (!cashAccount || !arAccount) {
      throw new Error("Required accounts not found");
    }

    const withholdingTaxAmount = args.withholdingTax || 0;
    const netPayment = args.paymentAmount - withholdingTaxAmount;

    // Journal Entries for Payment
    // 1. Debit: Cash/Bank
    await ctx.db.insert("journalEntries", {
      accountId: cashAccount._id,
      date: args.paymentDate,
      description: `Payment received - Invoice ${invoice.invoiceNumber}`,
      debit: netPayment,
      credit: 0,
      reference: args.referenceNumber,
      transactionType: "Payment",
      departmentId: "",
      costCenterId: ""
    });

    // 2. Debit: Creditable Withholding Tax (if applicable)
    if (withholdingTaxAmount > 0 && withholdingTaxAccount) {
      await ctx.db.insert("journalEntries", {
        accountId: withholdingTaxAccount._id,
        date: args.paymentDate,
        description: `Withholding Tax - Invoice ${invoice.invoiceNumber}`,
        debit: withholdingTaxAmount,
        credit: 0,
        reference: args.referenceNumber,
        transactionType: "Payment",
        departmentId: "",
        costCenterId: ""
      });
    }

    // 3. Credit: Accounts Receivable
    await ctx.db.insert("journalEntries", {
      accountId: arAccount._id,
      date: args.paymentDate,
      description: `Payment - Invoice ${invoice.invoiceNumber}`,
      debit: 0,
      credit: args.paymentAmount,
      reference: args.referenceNumber,
      transactionType: "Payment",
      departmentId: "",
      costCenterId: ""
    });

    // Update invoice payment status
    const newPaidAmount = invoice.paidAmount + args.paymentAmount;
    const newStatus = newPaidAmount >= invoice.totalAmount ? "Paid" : "Sent";

    await ctx.db.patch(args.invoiceId, {
      paidAmount: newPaidAmount,
      status: newStatus
    });

    // Record payment transaction
    await ctx.db.insert("payments", {
      invoiceId: args.invoiceId,
      date: args.paymentDate,
      amount: args.paymentAmount,
      method: args.paymentMethod,
      reference: args.referenceNumber
    });

    return {
      success: true,
      message: `✅ Payment recorded successfully\n\n💰 Payment Details:\n• Amount Paid: ₱${args.paymentAmount.toLocaleString()}\n• Withholding Tax: ₱${withholdingTaxAmount.toLocaleString()}\n• Net Received: ₱${netPayment.toLocaleString()}\n• Remaining Balance: ₱${(invoice.totalAmount - newPaidAmount).toLocaleString()}`,
      paidAmount: args.paymentAmount,
      totalPaid: newPaidAmount,
      remainingBalance: invoice.totalAmount - newPaidAmount,
      status: newStatus,
      withholdingTax: withholdingTaxAmount
    };
  }
});

// Get Revenue Tracking Summary
export const getRevenueTrackingSummary = query({
  args: {
    startDate: v.string(),
    endDate: v.string()
  },
  handler: async (ctx, args) => {
    const invoices = await ctx.db.query("customerInvoices")
      .filter(q => q.and(
        q.gte(q.field("invoiceDate"), args.startDate),
        q.lte(q.field("invoiceDate"), args.endDate)
      ))
      .collect();

    const customers = await ctx.db.query("customers").collect();
    const customerMap = new Map(customers.map(c => [c._id, c.name]));

    // Calculate revenue metrics
    const totalInvoiced = invoices.reduce((sum, inv) => sum + inv.totalAmount, 0);
    const totalCollected = invoices.reduce((sum, inv) => sum + inv.paidAmount, 0);
    const totalOutstanding = totalInvoiced - totalCollected;
    const totalVAT = invoices.reduce((sum, inv) => sum + (inv.outputVatAmount || 0), 0);

    // PFRS 15 Revenue Recognition metrics
    const totalRecognizedRevenue = invoices.reduce((sum, inv) => sum + (inv.revenueRecognizedAmount || 0), 0);
    const totalDeferredRevenue = invoices.reduce((sum, inv) => {
      const netAmount = inv.totalAmount - inv.taxAmount;
      const recognized = inv.revenueRecognizedAmount || 0;
      return sum + (netAmount - recognized);
    }, 0);

    // Status breakdown
    const statusBreakdown = {
      fullyRecognized: invoices.filter(inv => inv.revenueRecognitionStatus === "Fully Recognized").length,
      partiallyRecognized: invoices.filter(inv => inv.revenueRecognitionStatus === "Partially Recognized").length,
      notRecognized: invoices.filter(inv => !inv.revenueRecognitionStatus || inv.revenueRecognitionStatus === "Not Recognized").length
    };

    // Aging analysis
    const currentDate = new Date();
    const agingAnalysis = {
      current: 0,
      days30: 0,
      days60: 0,
      days90: 0,
      over90: 0
    };

    invoices.forEach(inv => {
      if (inv.status !== "Paid") {
        const dueDate = new Date(inv.dueDate);
        const daysPastDue = Math.floor((currentDate.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24));
        const outstanding = inv.totalAmount - inv.paidAmount;

        if (daysPastDue <= 0) agingAnalysis.current += outstanding;
        else if (daysPastDue <= 30) agingAnalysis.days30 += outstanding;
        else if (daysPastDue <= 60) agingAnalysis.days60 += outstanding;
        else if (daysPastDue <= 90) agingAnalysis.days90 += outstanding;
        else agingAnalysis.over90 += outstanding;
      }
    });

    return {
      period: { startDate: args.startDate, endDate: args.endDate },
      summary: {
        totalInvoices: invoices.length,
        totalInvoiced,
        totalCollected,
        totalOutstanding,
        totalVAT,
        collectionRate: totalInvoiced > 0 ? (totalCollected / totalInvoiced) * 100 : 0
      },
      pfrs15Metrics: {
        totalRecognizedRevenue,
        totalDeferredRevenue,
        recognitionRate: totalInvoiced > 0 ? (totalRecognizedRevenue / (totalInvoiced - totalVAT)) * 100 : 0,
        statusBreakdown
      },
      agingAnalysis,
      invoiceDetails: invoices.map(inv => ({
        invoiceNumber: inv.invoiceNumber,
        customerName: customerMap.get(inv.customerId) || "Unknown",
        invoiceDate: inv.invoiceDate,
        dueDate: inv.dueDate,
        totalAmount: inv.totalAmount,
        paidAmount: inv.paidAmount,
        outstandingAmount: inv.totalAmount - inv.paidAmount,
        status: inv.status,
        revenueRecognitionStatus: inv.revenueRecognitionStatus || "Not Recognized",
        recognizedAmount: inv.revenueRecognizedAmount || 0,
        deferredAmount: (inv.totalAmount - inv.taxAmount) - (inv.revenueRecognizedAmount || 0)
      }))
    };
  }
});

// ============================================================================
// EXPENSE MANAGEMENT - Philippine GAAP Compliant
// ============================================================================

// Record Purchase/Expense with Input VAT
export const recordPurchaseExpense = mutation({
  args: {
    supplierId: v.id("suppliers"),
    invoiceNumber: v.string(),
    invoiceDate: v.string(),
    dueDate: v.string(),
    expenseType: v.union(
      v.literal("Raw Materials"), v.literal("Office Supplies"), v.literal("Utilities"),
      v.literal("Professional Fees"), v.literal("Rent"), v.literal("Insurance"),
      v.literal("Repairs & Maintenance"), v.literal("Transportation"), v.literal("Other")
    ),
    lineItems: v.array(v.object({
      accountCode: v.string(),
      description: v.string(),
      amount: v.number(),
      vatType: v.union(v.literal("Vatable"), v.literal("VAT Exempt"), v.literal("Zero Rated"))
    })),
    vatableAmount: v.number(),
    vatExemptAmount: v.number(),
    zeroRatedAmount: v.number(),
    inputVAT: v.number(), // 12% VAT
    withholdingTax: v.number(), // Various rates depending on expense type
    totalAmount: v.number(),
    paymentTerms: v.string()
  },
  handler: async (ctx, args) => {
    // Get required accounts
    const accounts = await ctx.db.query("accounts").collect();
    const apAccount = accounts.find(acc => acc.code === "200" || acc.name.includes("Accounts Payable"));
    const inputVATAccount = accounts.find(acc => acc.code === "118" || acc.name.includes("Input Tax"));
    const withholdingTaxAccount = accounts.find(acc => acc.code === "119" || acc.name.includes("Creditable Withholding Tax"));

    if (!apAccount || !inputVATAccount) {
      throw new Error("Required accounts not found. Please ensure Chart of Accounts is properly set up.");
    }

    // Create vendor invoice
    const vendorInvoiceId = await ctx.db.insert("vendorInvoices", {
      supplierId: args.supplierId,
      invoiceNumber: args.invoiceNumber,
      invoiceDate: args.invoiceDate,
      dueDate: args.dueDate,
      totalAmount: args.totalAmount,
      paidAmount: 0,
      status: "Approved",
      description: `${args.expenseType} - ${args.invoiceNumber}`
    });

    // Journal Entries for Purchase/Expense
    const netPayable = args.totalAmount - args.withholdingTax;

    // 1. Debit: Expense/Asset Accounts (per line items)
    for (const item of args.lineItems) {
      const expenseAccount = accounts.find(acc => acc.code === item.accountCode);
      if (!expenseAccount) {
        throw new Error(`Account with code ${item.accountCode} not found`);
      }

      await ctx.db.insert("journalEntries", {
        accountId: expenseAccount._id,
        date: args.invoiceDate,
        description: `${item.description} - ${args.invoiceNumber}`,
        debit: item.amount,
        credit: 0,
        reference: args.invoiceNumber,
        transactionType: "Purchase",
        departmentId: "",
        costCenterId: ""
      });
    }

    // 2. Debit: Input VAT (if applicable)
    if (args.inputVAT > 0) {
      await ctx.db.insert("journalEntries", {
        accountId: inputVATAccount._id,
        date: args.invoiceDate,
        description: `Input VAT - ${args.invoiceNumber}`,
        debit: args.inputVAT,
        credit: 0,
        reference: args.invoiceNumber,
        transactionType: "Purchase",
        departmentId: "",
        costCenterId: ""
      });
    }

    // 3. Debit: Creditable Withholding Tax (if applicable)
    if (args.withholdingTax > 0 && withholdingTaxAccount) {
      await ctx.db.insert("journalEntries", {
        accountId: withholdingTaxAccount._id,
        date: args.invoiceDate,
        description: `Withholding Tax - ${args.invoiceNumber}`,
        debit: args.withholdingTax,
        credit: 0,
        reference: args.invoiceNumber,
        transactionType: "Purchase",
        departmentId: "",
        costCenterId: ""
      });
    }

    // 4. Credit: Accounts Payable
    await ctx.db.insert("journalEntries", {
      accountId: apAccount._id,
      date: args.invoiceDate,
      description: `Purchase - ${args.invoiceNumber}`,
      debit: 0,
      credit: netPayable,
      reference: args.invoiceNumber,
      transactionType: "Purchase",
      departmentId: "",
      costCenterId: ""
    });

    // Record VAT transaction for BIR compliance
    await ctx.db.insert("vatTransactions", {
      transactionDate: args.invoiceDate,
      transactionType: "Purchase",
      documentType: "Purchase Invoice",
      documentNumber: args.invoiceNumber,
      supplierId: args.supplierId,
      vatableAmount: args.vatableAmount,
      vatExemptAmount: args.vatExemptAmount,
      zeroRatedAmount: args.zeroRatedAmount,
      vatAmount: args.inputVAT,
      vatRate: 0.12, // 12% Philippine VAT
      withholdingTaxAmount: args.withholdingTax,
      withholdingTaxRate: args.withholdingTax / args.vatableAmount,
      netAmount: args.vatableAmount + args.vatExemptAmount + args.zeroRatedAmount,
      totalAmount: args.totalAmount
    });

    return {
      success: true,
      vendorInvoiceId,
      message: `✅ Purchase/Expense recorded successfully\n\n📋 Transaction Details:\n• Expense Amount: ₱${(args.vatableAmount + args.vatExemptAmount + args.zeroRatedAmount).toLocaleString()}\n• Input VAT: ₱${args.inputVAT.toLocaleString()}\n• Withholding Tax: ₱${args.withholdingTax.toLocaleString()}\n• Net Payable: ₱${netPayable.toLocaleString()}`,
      totalAmount: args.totalAmount,
      inputVAT: args.inputVAT,
      withholdingTax: args.withholdingTax,
      netPayable: netPayable,
      birCompliant: true
    };
  }
});

// Record Supplier Payment (Accounts Payable)
export const recordSupplierPayment = mutation({
  args: {
    vendorInvoiceId: v.id("vendorInvoices"),
    paymentDate: v.string(),
    paymentAmount: v.number(),
    paymentMethod: v.union(v.literal("Cash"), v.literal("Check"), v.literal("Bank Transfer")),
    referenceNumber: v.string(),
    bankAccount: v.optional(v.string())
  },
  handler: async (ctx, args) => {
    const vendorInvoice = await ctx.db.get(args.vendorInvoiceId);
    if (!vendorInvoice) {
      throw new Error("Vendor invoice not found");
    }

    const remainingBalance = vendorInvoice.totalAmount - vendorInvoice.paidAmount;
    if (args.paymentAmount > remainingBalance) {
      throw new Error(`Payment amount (₱${args.paymentAmount.toLocaleString()}) exceeds remaining balance (₱${remainingBalance.toLocaleString()})`);
    }

    // Get required accounts
    const accounts = await ctx.db.query("accounts").collect();
    const cashAccount = accounts.find(acc => acc.code === "111" || acc.name.includes("Cash"));
    const apAccount = accounts.find(acc => acc.code === "200" || acc.name.includes("Accounts Payable"));

    if (!cashAccount || !apAccount) {
      throw new Error("Required accounts not found");
    }

    // Journal Entries for Payment
    // 1. Debit: Accounts Payable
    await ctx.db.insert("journalEntries", {
      accountId: apAccount._id,
      date: args.paymentDate,
      description: `Payment - Invoice ${vendorInvoice.invoiceNumber}`,
      debit: args.paymentAmount,
      credit: 0,
      reference: args.referenceNumber,
      transactionType: "Payment",
      departmentId: "",
      costCenterId: ""
    });

    // 2. Credit: Cash/Bank
    await ctx.db.insert("journalEntries", {
      accountId: cashAccount._id,
      date: args.paymentDate,
      description: `Payment to supplier - Invoice ${vendorInvoice.invoiceNumber}`,
      debit: 0,
      credit: args.paymentAmount,
      reference: args.referenceNumber,
      transactionType: "Payment",
      departmentId: "",
      costCenterId: ""
    });

    // Update vendor invoice payment status
    const newPaidAmount = vendorInvoice.paidAmount + args.paymentAmount;
    const newStatus = newPaidAmount >= vendorInvoice.totalAmount ? "Paid" : "Approved";

    await ctx.db.patch(args.vendorInvoiceId, {
      paidAmount: newPaidAmount,
      status: newStatus
    });

    return {
      success: true,
      message: `✅ Supplier payment recorded successfully\n\n💰 Payment Details:\n• Amount Paid: ₱${args.paymentAmount.toLocaleString()}\n• Total Paid: ₱${newPaidAmount.toLocaleString()}\n• Remaining Balance: ₱${(vendorInvoice.totalAmount - newPaidAmount).toLocaleString()}`,
      paidAmount: args.paymentAmount,
      totalPaid: newPaidAmount,
      remainingBalance: vendorInvoice.totalAmount - newPaidAmount,
      status: newStatus
    };
  }
});

// Get Expense Management Summary
export const getExpenseManagementSummary = query({
  args: {
    startDate: v.string(),
    endDate: v.string()
  },
  handler: async (ctx, args) => {
    const vendorInvoices = await ctx.db.query("vendorInvoices")
      .filter(q => q.and(
        q.gte(q.field("invoiceDate"), args.startDate),
        q.lte(q.field("invoiceDate"), args.endDate)
      ))
      .collect();

    const suppliers = await ctx.db.query("suppliers").collect();
    const supplierMap = new Map(suppliers.map(s => [s._id, s.name]));

    // Calculate expense metrics
    const totalExpenses = vendorInvoices.reduce((sum, inv) => sum + inv.totalAmount, 0);
    const totalPaid = vendorInvoices.reduce((sum, inv) => sum + inv.paidAmount, 0);
    const totalOutstanding = totalExpenses - totalPaid;

    // Get VAT transactions for the period
    const vatTransactions = await ctx.db.query("vatTransactions")
      .filter(q => q.and(
        q.eq(q.field("transactionType"), "Purchase"),
        q.gte(q.field("transactionDate"), args.startDate),
        q.lte(q.field("transactionDate"), args.endDate)
      ))
      .collect();

    const totalInputVAT = vatTransactions.reduce((sum, vt) => sum + vt.vatAmount, 0);
    const totalWithholdingTax = vatTransactions.reduce((sum, vt) => sum + vt.withholdingTaxAmount, 0);

    // Aging analysis for payables
    const currentDate = new Date();
    const payableAging = {
      current: 0,
      days30: 0,
      days60: 0,
      days90: 0,
      over90: 0
    };

    vendorInvoices.forEach(inv => {
      if (inv.status !== "Paid") {
        const dueDate = new Date(inv.dueDate);
        const daysPastDue = Math.floor((currentDate.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24));
        const outstanding = inv.totalAmount - inv.paidAmount;

        if (daysPastDue <= 0) payableAging.current += outstanding;
        else if (daysPastDue <= 30) payableAging.days30 += outstanding;
        else if (daysPastDue <= 60) payableAging.days60 += outstanding;
        else if (daysPastDue <= 90) payableAging.days90 += outstanding;
        else payableAging.over90 += outstanding;
      }
    });

    return {
      period: { startDate: args.startDate, endDate: args.endDate },
      summary: {
        totalInvoices: vendorInvoices.length,
        totalExpenses,
        totalPaid,
        totalOutstanding,
        totalInputVAT,
        totalWithholdingTax,
        paymentRate: totalExpenses > 0 ? (totalPaid / totalExpenses) * 100 : 0
      },
      payableAging,
      invoiceDetails: vendorInvoices.map(inv => ({
        invoiceNumber: inv.invoiceNumber,
        supplierName: supplierMap.get(inv.supplierId) || "Unknown",
        invoiceDate: inv.invoiceDate,
        dueDate: inv.dueDate,
        totalAmount: inv.totalAmount,
        paidAmount: inv.paidAmount,
        outstandingAmount: inv.totalAmount - inv.paidAmount,
        status: inv.status,
        description: inv.description
      }))
    };
  }
});
