import { query } from "./_generated/server";
import { v } from "convex/values";

// ============================================================================
// FINANCIAL REPORTING SYSTEM
// Philippine GAAP Compliant • PFRS Format • BIR Ready Reports
// ============================================================================

// Generate Trial Balance
export const getTrialBalance = query({
  args: {
    asOfDate: v.string(),
    fiscalPeriodId: v.optional(v.id("fiscalPeriods"))
  },
  handler: async (ctx, args) => {
    const accounts = await ctx.db.query("accounts").collect();
    
    // Get fiscal period
    let fiscalPeriod = null;
    if (args.fiscalPeriodId) {
      fiscalPeriod = await ctx.db.get(args.fiscalPeriodId);
    } else {
      fiscalPeriod = await ctx.db.query("fiscalPeriods")
        .filter(q => q.and(
          q.lte(q.field("startDate"), args.asOfDate),
          q.gte(q.field("endDate"), args.asOfDate)
        ))
        .first();
    }

    // Calculate balances from journal entries
    const journalEntries = await ctx.db.query("journalEntries")
      .filter(q => q.and(
        q.lte(q.field("date"), args.asOfDate),
        q.eq(q.field("isPosted"), true)
      ))
      .collect();

    // Group by account
    const accountBalances = new Map<string, { debit: number; credit: number; balance: number }>();
    
    accounts.forEach(account => {
      accountBalances.set(account._id, { debit: 0, credit: 0, balance: 0 });
    });

    journalEntries.forEach(entry => {
      const current = accountBalances.get(entry.accountId) || { debit: 0, credit: 0, balance: 0 };
      current.debit += entry.debit;
      current.credit += entry.credit;
      current.balance = current.debit - current.credit;
      accountBalances.set(entry.accountId, current);
    });

    // Prepare trial balance data
    const trialBalanceData = accounts.map(account => {
      const balances = accountBalances.get(account._id) || { debit: 0, credit: 0, balance: 0 };
      
      return {
        accountCode: account.code,
        accountName: account.name,
        accountType: account.type,
        accountSubType: account.subType || "",
        normalBalance: account.normalBalance,
        debitBalance: balances.balance > 0 ? balances.balance : 0,
        creditBalance: balances.balance < 0 ? Math.abs(balances.balance) : 0,
        totalDebits: balances.debit,
        totalCredits: balances.credit,
        netMovement: balances.debit - balances.credit
      };
    }).sort((a, b) => a.accountCode.localeCompare(b.accountCode));

    // Calculate totals
    const totalDebitBalances = trialBalanceData.reduce((sum, item) => sum + item.debitBalance, 0);
    const totalCreditBalances = trialBalanceData.reduce((sum, item) => sum + item.creditBalance, 0);
    const totalDebitEntries = trialBalanceData.reduce((sum, item) => sum + item.totalDebits, 0);
    const totalCreditEntries = trialBalanceData.reduce((sum, item) => sum + item.totalCredits, 0);

    return {
      reportTitle: "Trial Balance",
      companyName: "XYZ Manufacturing Ltd.",
      asOfDate: args.asOfDate,
      fiscalPeriod: fiscalPeriod?.periodName || "Current Period",
      currency: "PHP",
      accounts: trialBalanceData,
      totals: {
        totalDebitBalances,
        totalCreditBalances,
        totalDebitEntries,
        totalCreditEntries,
        isBalanced: Math.abs(totalDebitBalances - totalCreditBalances) < 0.01 && 
                   Math.abs(totalDebitEntries - totalCreditEntries) < 0.01
      },
      philippineGAAPCompliant: true,
      generatedAt: new Date().toISOString()
    };
  }
});

// Generate Statement of Financial Position (Balance Sheet)
export const getStatementOfFinancialPosition = query({
  args: {
    asOfDate: v.string(),
    fiscalPeriodId: v.optional(v.id("fiscalPeriods"))
  },
  handler: async (ctx, args) => {
    const accounts = await ctx.db.query("accounts").collect();
    const journalEntries = await ctx.db.query("journalEntries")
      .filter(q => q.and(
        q.lte(q.field("date"), args.asOfDate),
        q.eq(q.field("isPosted"), true)
      ))
      .collect();

    // Calculate account balances
    const accountBalances = new Map<string, number>();
    
    journalEntries.forEach(entry => {
      const currentBalance = accountBalances.get(entry.accountId) || 0;
      accountBalances.set(entry.accountId, currentBalance + entry.debit - entry.credit);
    });

    // Initialize financial position structure
    const assets = {
      currentAssets: {
        cash: 0,
        accountsReceivable: 0,
        inventory: 0,
        prepaidExpenses: 0,
        inputTax: 0,
        creditableWithholdingTax: 0,
        otherCurrentAssets: 0,
        total: 0
      },
      nonCurrentAssets: {
        propertyPlantEquipment: 0,
        accumulatedDepreciation: 0,
        netPPE: 0,
        intangibleAssets: 0,
        otherNonCurrentAssets: 0,
        total: 0
      },
      totalAssets: 0
    };

    const liabilities = {
      currentLiabilities: {
        accountsPayable: 0,
        outputTax: 0,
        contractLiability: 0,
        accruedExpenses: 0,
        shortTermLoans: 0,
        otherCurrentLiabilities: 0,
        total: 0
      },
      nonCurrentLiabilities: {
        longTermLoans: 0,
        otherNonCurrentLiabilities: 0,
        total: 0
      },
      totalLiabilities: 0
    };

    const equity = {
      capitalStock: 0,
      retainedEarnings: 0,
      currentYearEarnings: 0,
      otherComprehensiveIncome: 0,
      totalEquity: 0
    };

    // Map accounts to financial statement line items
    accounts.forEach(account => {
      const balance = accountBalances.get(account._id) || 0;
      
      if (account.type === "Asset") {
        if (account.subType === "Current Asset") {
          switch (account.code) {
            case "111":
            case "112":
              assets.currentAssets.cash += balance;
              break;
            case "113":
              assets.currentAssets.accountsReceivable += balance;
              break;
            case "115":
              assets.currentAssets.inventory += balance;
              break;
            case "117":
              assets.currentAssets.prepaidExpenses += balance;
              break;
            case "118":
              assets.currentAssets.inputTax += balance;
              break;
            case "119":
              assets.currentAssets.creditableWithholdingTax += balance;
              break;
            default:
              assets.currentAssets.otherCurrentAssets += balance;
          }
        } else if (account.subType === "Fixed Asset") {
          if (account.name.toLowerCase().includes("accumulated depreciation")) {
            assets.nonCurrentAssets.accumulatedDepreciation += Math.abs(balance);
          } else {
            assets.nonCurrentAssets.propertyPlantEquipment += balance;
          }
        }
      } else if (account.type === "Liability") {
        if (account.subType === "Current Liability") {
          switch (account.code) {
            case "200":
              liabilities.currentLiabilities.accountsPayable += Math.abs(balance);
              break;
            case "201":
              liabilities.currentLiabilities.outputTax += Math.abs(balance);
              break;
            case "202":
              liabilities.currentLiabilities.contractLiability += Math.abs(balance);
              break;
            default:
              liabilities.currentLiabilities.otherCurrentLiabilities += Math.abs(balance);
          }
        } else {
          liabilities.nonCurrentLiabilities.otherNonCurrentLiabilities += Math.abs(balance);
        }
      } else if (account.type === "Equity") {
        if (account.name.toLowerCase().includes("capital")) {
          equity.capitalStock += Math.abs(balance);
        } else {
          equity.retainedEarnings += Math.abs(balance);
        }
      }
    });

    // Calculate current year earnings (Revenue - Expenses)
    const currentYearStart = new Date(args.asOfDate).getFullYear() + "-01-01";
    const currentYearEntries = journalEntries.filter(entry => 
      entry.date >= currentYearStart && entry.date <= args.asOfDate
    );

    let currentYearRevenue = 0;
    let currentYearExpenses = 0;

    currentYearEntries.forEach(entry => {
      const account = accounts.find(acc => acc._id === entry.accountId);
      if (account) {
        if (account.type === "Revenue") {
          currentYearRevenue += entry.credit - entry.debit;
        } else if (account.type === "Expense") {
          currentYearExpenses += entry.debit - entry.credit;
        }
      }
    });

    equity.currentYearEarnings = currentYearRevenue - currentYearExpenses;

    // Calculate totals
    assets.currentAssets.total = Object.values(assets.currentAssets).reduce((sum, val) => 
      typeof val === 'number' ? sum + val : sum, 0) - assets.currentAssets.total;
    
    assets.nonCurrentAssets.netPPE = assets.nonCurrentAssets.propertyPlantEquipment - assets.nonCurrentAssets.accumulatedDepreciation;
    assets.nonCurrentAssets.total = assets.nonCurrentAssets.netPPE + assets.nonCurrentAssets.intangibleAssets + assets.nonCurrentAssets.otherNonCurrentAssets;
    
    assets.totalAssets = assets.currentAssets.total + assets.nonCurrentAssets.total;

    liabilities.currentLiabilities.total = Object.values(liabilities.currentLiabilities).reduce((sum, val) => 
      typeof val === 'number' ? sum + val : sum, 0) - liabilities.currentLiabilities.total;
    
    liabilities.nonCurrentLiabilities.total = Object.values(liabilities.nonCurrentLiabilities).reduce((sum, val) => 
      typeof val === 'number' ? sum + val : sum, 0) - liabilities.nonCurrentLiabilities.total;
    
    liabilities.totalLiabilities = liabilities.currentLiabilities.total + liabilities.nonCurrentLiabilities.total;

    equity.totalEquity = equity.capitalStock + equity.retainedEarnings + equity.currentYearEarnings + equity.otherComprehensiveIncome;

    return {
      reportTitle: "Statement of Financial Position",
      companyName: "XYZ Manufacturing Ltd.",
      asOfDate: args.asOfDate,
      currency: "PHP",
      pfrsCompliant: true,
      assets,
      liabilities,
      equity,
      totalLiabilitiesAndEquity: liabilities.totalLiabilities + equity.totalEquity,
      balanceCheck: Math.abs(assets.totalAssets - (liabilities.totalLiabilities + equity.totalEquity)) < 0.01,
      generatedAt: new Date().toISOString()
    };
  }
});

// Generate Statement of Comprehensive Income
export const getStatementOfComprehensiveIncome = query({
  args: {
    startDate: v.string(),
    endDate: v.string(),
    fiscalPeriodId: v.optional(v.id("fiscalPeriods"))
  },
  handler: async (ctx, args) => {
    const accounts = await ctx.db.query("accounts").collect();
    const journalEntries = await ctx.db.query("journalEntries")
      .filter(q => q.and(
        q.gte(q.field("date"), args.startDate),
        q.lte(q.field("date"), args.endDate),
        q.eq(q.field("isPosted"), true)
      ))
      .collect();

    // Calculate account balances for the period
    const accountBalances = new Map<string, number>();
    
    journalEntries.forEach(entry => {
      const currentBalance = accountBalances.get(entry.accountId) || 0;
      accountBalances.set(entry.accountId, currentBalance + entry.debit - entry.credit);
    });

    const income = {
      revenue: {
        salesRevenue: 0,
        otherRevenue: 0,
        totalRevenue: 0
      },
      costOfSales: {
        costOfGoodsSold: 0,
        totalCostOfSales: 0
      },
      grossProfit: 0,
      operatingExpenses: {
        salariesAndWages: 0,
        rentExpense: 0,
        utilitiesExpense: 0,
        depreciationExpense: 0,
        professionalFees: 0,
        officeSupplies: 0,
        repairsAndMaintenance: 0,
        transportationExpense: 0,
        otherOperatingExpenses: 0,
        totalOperatingExpenses: 0
      },
      operatingIncome: 0,
      otherIncomeExpenses: {
        interestIncome: 0,
        interestExpense: 0,
        gainLossOnAssetDisposal: 0,
        otherNonOperating: 0,
        totalOtherIncomeExpenses: 0
      },
      incomeBeforeTax: 0,
      incomeTaxExpense: 0,
      netIncome: 0,
      otherComprehensiveIncome: 0,
      totalComprehensiveIncome: 0
    };

    // Map accounts to income statement line items
    accounts.forEach(account => {
      const balance = accountBalances.get(account._id) || 0;
      
      if (account.type === "Revenue") {
        if (account.code === "400" || account.name.toLowerCase().includes("sales")) {
          income.revenue.salesRevenue += Math.abs(balance);
        } else {
          income.revenue.otherRevenue += Math.abs(balance);
        }
      } else if (account.type === "Expense") {
        if (account.subType === "Cost of Goods Sold") {
          income.costOfSales.costOfGoodsSold += balance;
        } else if (account.subType === "Operating Expense") {
          switch (account.code) {
            case "512":
              income.operatingExpenses.salariesAndWages += balance;
              break;
            case "511":
              income.operatingExpenses.rentExpense += balance;
              break;
            case "510":
              income.operatingExpenses.utilitiesExpense += balance;
              break;
            case "506":
              income.operatingExpenses.depreciationExpense += balance;
              break;
            default:
              income.operatingExpenses.otherOperatingExpenses += balance;
          }
        }
      }
    });

    // Calculate totals and derived amounts
    income.revenue.totalRevenue = income.revenue.salesRevenue + income.revenue.otherRevenue;
    income.costOfSales.totalCostOfSales = income.costOfSales.costOfGoodsSold;
    income.grossProfit = income.revenue.totalRevenue - income.costOfSales.totalCostOfSales;
    
    income.operatingExpenses.totalOperatingExpenses = Object.values(income.operatingExpenses).reduce((sum, val) => 
      typeof val === 'number' ? sum + val : sum, 0) - income.operatingExpenses.totalOperatingExpenses;
    
    income.operatingIncome = income.grossProfit - income.operatingExpenses.totalOperatingExpenses;
    income.incomeBeforeTax = income.operatingIncome + income.otherIncomeExpenses.totalOtherIncomeExpenses;
    income.netIncome = income.incomeBeforeTax - income.incomeTaxExpense;
    income.totalComprehensiveIncome = income.netIncome + income.otherComprehensiveIncome;

    return {
      reportTitle: "Statement of Comprehensive Income",
      companyName: "XYZ Manufacturing Ltd.",
      period: { startDate: args.startDate, endDate: args.endDate },
      currency: "PHP",
      pfrsCompliant: true,
      income,
      keyRatios: {
        grossProfitMargin: income.revenue.totalRevenue > 0 ? (income.grossProfit / income.revenue.totalRevenue) * 100 : 0,
        operatingMargin: income.revenue.totalRevenue > 0 ? (income.operatingIncome / income.revenue.totalRevenue) * 100 : 0,
        netProfitMargin: income.revenue.totalRevenue > 0 ? (income.netIncome / income.revenue.totalRevenue) * 100 : 0
      },
      generatedAt: new Date().toISOString()
    };
  }
});
