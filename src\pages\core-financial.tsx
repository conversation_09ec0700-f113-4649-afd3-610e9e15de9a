import React, { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import Sidebar from "../components/sidebar";
import Navbar from "../components/navbar";

function CoreFinancial() {
  const [activeTab, setActiveTab] = useState("overview");
  const [dateRange, setDateRange] = useState({
    startDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0]
  });

  // Mutations for financial operations
  const addAccountBalance = useMutation(api.financialManagement.addAccountBalance);
  const setOpeningBalances = useMutation(api.financialManagement.setOpeningBalances);
  const recordSalesTransaction = useMutation(api.financialManagement.recordSalesTransaction);
  const recordPurchaseTransaction = useMutation(api.expenseManagement.recordPurchaseTransaction);
  const calculateMonthlyDepreciation = useMutation(api.assetManagement.calculateMonthlyDepreciation);
  const createFiscalPeriod = useMutation(api.fiscalPeriodManagement.createFiscalPeriod);

  // Queries for financial data
  const trialBalance = useQuery(api.financialReporting.getTrialBalance, {
    asOfDate: dateRange.endDate
  });
  const financialPosition = useQuery(api.financialReporting.getStatementOfFinancialPosition, {
    asOfDate: dateRange.endDate
  });
  const vatSummary = useQuery(api.birCompliance.getVATSummaryReport, {
    startDate: dateRange.startDate,
    endDate: dateRange.endDate
  });
  const currentPeriod = useQuery(api.fiscalPeriodManagement.getCurrentFiscalPeriod);

  // Form states
  const [openingBalanceForm, setOpeningBalanceForm] = useState({
    accountCode: "",
    amount: 0,
    description: "",
    date: new Date().toISOString().split('T')[0],
    referenceNumber: ""
  });

  const [salesForm, setSalesForm] = useState({
    customerId: "",
    invoiceNumber: "",
    invoiceDate: new Date().toISOString().split('T')[0],
    dueDate: "",
    vatableAmount: 0,
    vatExemptAmount: 0,
    zeroRatedAmount: 0,
    outputVAT: 0,
    totalAmount: 0,
    paymentTerms: "Net 30"
  });

  const [purchaseForm, setPurchaseForm] = useState({
    supplierId: "",
    invoiceNumber: "",
    invoiceDate: new Date().toISOString().split('T')[0],
    dueDate: "",
    vatableAmount: 0,
    vatExemptAmount: 0,
    zeroRatedAmount: 0,
    inputVAT: 0,
    withholdingTax: 0,
    withholdingTaxRate: 0.02,
    totalAmount: 0,
    paymentTerms: "Net 30"
  });

  const [message, setMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  // Handle opening balance submission
  const handleAddOpeningBalance = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    try {
      const result = await addAccountBalance(openingBalanceForm);
      setMessage(result.message);
      setOpeningBalanceForm({
        accountCode: "",
        amount: 0,
        description: "",
        date: new Date().toISOString().split('T')[0],
        referenceNumber: ""
      });
    } catch (error) {
      setMessage(`Error: ${error}`);
    }
    setIsLoading(false);
  };

  // Handle sales transaction submission
  const handleRecordSales = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    try {
      // Calculate VAT automatically
      const calculatedOutputVAT = salesForm.vatableAmount * 0.12;
      const calculatedTotal = salesForm.vatableAmount + salesForm.vatExemptAmount + salesForm.zeroRatedAmount + calculatedOutputVAT;
      
      const result = await recordSalesTransaction({
        ...salesForm,
        outputVAT: calculatedOutputVAT,
        totalAmount: calculatedTotal,
        lineItems: [{
          description: "Sales Transaction",
          quantity: 1,
          unitPrice: salesForm.vatableAmount + salesForm.vatExemptAmount + salesForm.zeroRatedAmount,
          amount: salesForm.vatableAmount + salesForm.vatExemptAmount + salesForm.zeroRatedAmount,
          vatType: "Vatable" as const
        }],
        performanceObligations: [{
          description: "Goods/Services Delivery",
          allocatedAmount: salesForm.vatableAmount + salesForm.vatExemptAmount + salesForm.zeroRatedAmount,
          deliveryDate: salesForm.dueDate,
          satisfactionMethod: "Point in Time" as const
        }]
      });
      setMessage(result.message);
      setSalesForm({
        customerId: "",
        invoiceNumber: "",
        invoiceDate: new Date().toISOString().split('T')[0],
        dueDate: "",
        vatableAmount: 0,
        vatExemptAmount: 0,
        zeroRatedAmount: 0,
        outputVAT: 0,
        totalAmount: 0,
        paymentTerms: "Net 30"
      });
    } catch (error) {
      setMessage(`Error: ${error}`);
    }
    setIsLoading(false);
  };

  // Handle purchase transaction submission
  const handleRecordPurchase = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    try {
      // Calculate VAT and withholding tax automatically
      const calculatedInputVAT = purchaseForm.vatableAmount * 0.12;
      const calculatedWithholdingTax = purchaseForm.vatableAmount * purchaseForm.withholdingTaxRate;
      const calculatedTotal = purchaseForm.vatableAmount + purchaseForm.vatExemptAmount + purchaseForm.zeroRatedAmount + calculatedInputVAT;
      
      const result = await recordPurchaseTransaction({
        ...purchaseForm,
        inputVAT: calculatedInputVAT,
        withholdingTax: calculatedWithholdingTax,
        totalAmount: calculatedTotal,
        lineItems: [{
          accountCode: "500", // Purchases account
          description: "Purchase Transaction",
          amount: purchaseForm.vatableAmount + purchaseForm.vatExemptAmount + purchaseForm.zeroRatedAmount,
          vatType: "Vatable" as const
        }]
      });
      setMessage(result.message);
      setPurchaseForm({
        supplierId: "",
        invoiceNumber: "",
        invoiceDate: new Date().toISOString().split('T')[0],
        dueDate: "",
        vatableAmount: 0,
        vatExemptAmount: 0,
        zeroRatedAmount: 0,
        inputVAT: 0,
        withholdingTax: 0,
        withholdingTaxRate: 0.02,
        totalAmount: 0,
        paymentTerms: "Net 30"
      });
    } catch (error) {
      setMessage(`Error: ${error}`);
    }
    setIsLoading(false);
  };

  // Handle monthly depreciation calculation
  const handleCalculateDepreciation = async () => {
    setIsLoading(true);
    try {
      const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM format
      const result = await calculateMonthlyDepreciation({ periodDate: currentMonth });
      setMessage(result.message);
    } catch (error) {
      setMessage(`Error: ${error}`);
    }
    setIsLoading(false);
  };

  // Set common opening balances
  const handleSetCommonOpeningBalances = async () => {
    setIsLoading(true);
    try {
      const result = await setOpeningBalances({
        balances: [
          { accountCode: "111", amount: 500000 }, // Cash
          { accountCode: "300", amount: 500000 }  // Capital Stock
        ],
        date: new Date().toISOString().split('T')[0],
        description: "Initial Opening Balances",
        referenceNumber: "OB-001"
      });
      setMessage(result.message);
    } catch (error) {
      setMessage(`Error: ${error}`);
    }
    setIsLoading(false);
  };

  if (!trialBalance || !financialPosition || !vatSummary) {
    return (
      <div className="flex h-screen bg-gradient-to-br from-slate-50 to-blue-50">
        <Sidebar />
        <div className="flex-1 flex flex-col">
          <Navbar />
          <main className="p-8 flex-1 overflow-auto">
            <div className="max-w-7xl mx-auto">
              <div className="text-center py-16">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-6">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>
                <h1 className="text-3xl font-bold text-gray-900 mb-4">Comprehensive Financial System</h1>
                <p className="text-lg text-gray-600">Loading financial data...</p>
              </div>
            </div>
          </main>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      <Sidebar />
      <div className="flex-1 flex flex-col">
        <Navbar />
        <main className="p-8 flex-1 overflow-auto">
          <div className="max-w-7xl mx-auto">
            {/* Header */}
            <div className="mb-8">
              <h1 className="text-4xl font-bold text-gray-900 mb-2">
                Comprehensive Financial System
              </h1>
              <p className="text-lg text-gray-600">
                🇵🇭 Philippine GAAP • PFRS/PAS Compliant • BIR Ready • Complete Financial Management
              </p>
            </div>

            {/* Current Period Info */}
            {currentPeriod?.currentPeriod && (
              <div className="bg-blue-50 border border-blue-200 rounded-xl p-4 mb-8">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-semibold text-blue-900">Current Fiscal Period</h3>
                    <p className="text-blue-700">{currentPeriod.currentPeriod.periodName} • {currentPeriod.currentPeriod.status}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-blue-600">Days Remaining</p>
                    <p className="text-2xl font-bold text-blue-900">{currentPeriod.currentPeriod.daysRemaining}</p>
                  </div>
                </div>
              </div>
            )}

            {/* Message Display */}
            {message && (
              <div className="bg-green-50 border border-green-200 rounded-xl p-4 mb-8">
                <pre className="text-green-800 whitespace-pre-wrap text-sm">{message}</pre>
                <button 
                  onClick={() => setMessage("")}
                  className="mt-2 text-green-600 hover:text-green-800 text-sm"
                >
                  ✕ Close
                </button>
              </div>
            )}

            {/* Navigation Tabs */}
            <div className="flex flex-wrap gap-3 mb-8">
              <button
                type="button"
                onClick={() => setActiveTab("overview")}
                className={`inline-flex items-center px-6 py-3 rounded-xl font-semibold transition-all duration-200 shadow-sm ${
                  activeTab === "overview"
                    ? "bg-blue-600 text-white shadow-lg shadow-blue-200 transform scale-105"
                    : "bg-white text-gray-700 border border-gray-200 hover:bg-blue-50 hover:border-blue-300"
                }`}
              >
                📊 Financial Overview
              </button>
              <button
                type="button"
                onClick={() => setActiveTab("transactions")}
                className={`inline-flex items-center px-6 py-3 rounded-xl font-semibold transition-all duration-200 shadow-sm ${
                  activeTab === "transactions"
                    ? "bg-green-600 text-white shadow-lg shadow-green-200 transform scale-105"
                    : "bg-white text-gray-700 border border-gray-200 hover:bg-green-50 hover:border-green-300"
                }`}
              >
                💰 Record Transactions
              </button>
              <button
                type="button"
                onClick={() => setActiveTab("reports")}
                className={`inline-flex items-center px-6 py-3 rounded-xl font-semibold transition-all duration-200 shadow-sm ${
                  activeTab === "reports"
                    ? "bg-purple-600 text-white shadow-lg shadow-purple-200 transform scale-105"
                    : "bg-white text-gray-700 border border-gray-200 hover:bg-purple-50 hover:border-purple-300"
                }`}
              >
                📋 Financial Reports
              </button>
              <button
                type="button"
                onClick={() => setActiveTab("setup")}
                className={`inline-flex items-center px-6 py-3 rounded-xl font-semibold transition-all duration-200 shadow-sm ${
                  activeTab === "setup"
                    ? "bg-orange-600 text-white shadow-lg shadow-orange-200 transform scale-105"
                    : "bg-white text-gray-700 border border-gray-200 hover:bg-orange-50 hover:border-orange-300"
                }`}
              >
                ⚙️ System Setup
              </button>
            </div>
