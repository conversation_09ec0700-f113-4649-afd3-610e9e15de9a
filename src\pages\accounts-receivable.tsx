import React, { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import Sidebar from "../components/sidebar";
import Navbar from "../components/navbar";

function AccountsReceivable() {
  const [activeTab, setActiveTab] = useState<'overview' | 'invoices' | 'customers' | 'aging'>('overview');
  const [showNewInvoiceModal, setShowNewInvoiceModal] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [selectedInvoice, setSelectedInvoice] = useState<any>(null);

  // Form states
  const [invoiceFormData, setInvoiceFormData] = useState({
    customerId: "",
    invoiceNumber: "",
    invoiceDate: new Date().toISOString().split('T')[0],
    dueDate: "",
    totalAmount: 0,
    taxAmount: 0,
    discountAmount: 0,
    salesOrderId: ""
  });

  const [paymentFormData, setPaymentFormData] = useState({
    amount: 0,
    paymentDate: new Date().toISOString().split('T')[0],
    paymentMethod: "Bank Transfer",
    reference: ""
  });

  // Fetch data
  const receivableData = useQuery(api.tasks.getAccountsReceivableReport);
  const customers = useQuery(api.tasks.getCustomers);
  const dashboardData = useQuery(api.tasks.getDashboardData);

  // Mutations
  const addCustomerInvoice = useMutation(api.tasks.addCustomerInvoice);
  const recordCustomerPayment = useMutation(api.tasks.recordCustomerPayment);

  // Handlers
  const handleCreateInvoice = async (e: React.FormEvent) => {
    e.preventDefault();

    // Frontend validation
    if (!invoiceFormData.customerId) {
      alert("Please select a customer");
      return;
    }

    if (!invoiceFormData.invoiceNumber.trim()) {
      alert("Please enter an invoice number");
      return;
    }

    if (invoiceFormData.totalAmount <= 0) {
      alert("Total amount must be greater than 0");
      return;
    }

    if (new Date(invoiceFormData.dueDate) <= new Date(invoiceFormData.invoiceDate)) {
      alert("Due date must be after invoice date");
      return;
    }

    try {
      const result = await addCustomerInvoice({
        customerId: invoiceFormData.customerId as any,
        invoiceNumber: invoiceFormData.invoiceNumber,
        invoiceDate: invoiceFormData.invoiceDate,
        dueDate: invoiceFormData.dueDate,
        totalAmount: invoiceFormData.totalAmount,
        taxAmount: invoiceFormData.taxAmount,
        discountAmount: invoiceFormData.discountAmount,
        salesOrderId: invoiceFormData.salesOrderId || undefined
      });

      setShowNewInvoiceModal(false);
      setInvoiceFormData({
        customerId: "",
        invoiceNumber: "",
        invoiceDate: new Date().toISOString().split('T')[0],
        dueDate: "",
        totalAmount: 0,
        taxAmount: 0,
        discountAmount: 0,
        salesOrderId: ""
      });

      // Show enhanced success message
      if (result && result.message) {
        alert(`✅ ${result.message}\n\n📊 Journal Entries Created: ${result.journalEntriesCreated}\n💰 Total Amount: $${result.totalAmount.toLocaleString()}\n📈 Net Revenue: $${result.netAmount.toLocaleString()}\n🏛️ Tax Amount: $${result.taxAmount.toLocaleString()}\n\n📋 Financial reports have been automatically updated.`);
      } else {
        alert("Invoice created successfully!");
      }
    } catch (error) {
      alert("Error creating invoice: " + error);
    }
  };

  const handleRecordPayment = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedInvoice) return;

    // Frontend validation
    if (paymentFormData.amount <= 0) {
      alert("Payment amount must be greater than 0");
      return;
    }

    const outstandingAmount = selectedInvoice.outstandingAmount || (selectedInvoice.totalAmount - selectedInvoice.paidAmount);
    if (paymentFormData.amount > outstandingAmount) {
      alert(`Payment amount ($${paymentFormData.amount.toLocaleString()}) cannot exceed outstanding amount ($${outstandingAmount.toLocaleString()})`);
      return;
    }

    if (!paymentFormData.paymentDate) {
      alert("Please select a payment date");
      return;
    }

    try {
      const result = await recordCustomerPayment({
        invoiceId: selectedInvoice._id,
        amount: paymentFormData.amount,
        paymentDate: paymentFormData.paymentDate,
        paymentMethod: paymentFormData.paymentMethod,
        reference: paymentFormData.reference || undefined
      });

      setShowPaymentModal(false);
      setSelectedInvoice(null);
      setPaymentFormData({
        amount: 0,
        paymentDate: new Date().toISOString().split('T')[0],
        paymentMethod: "Bank Transfer",
        reference: ""
      });

      // Show enhanced success message with detailed information
      if (result && result.message) {
        alert(`✅ Payment Recorded Successfully!\n\n${result.message}\n\n📋 Invoice: ${result.invoiceNumber}\n👤 Customer: ${result.customerName}\n💰 Payment Amount: $${result.paymentAmount.toLocaleString()}\n📅 Payment Date: ${result.paymentDate}\n💳 Method: ${result.paymentMethod}\n📊 New Status: ${result.newStatus}\n💵 Total Paid: $${result.newPaidAmount.toLocaleString()}\n🔄 Outstanding: $${result.outstandingAmount.toLocaleString()}\n\n📈 Financial reports, trial balance, and general ledger have been automatically updated.`);
      } else {
        alert("Payment recorded successfully! Financial reports have been updated.");
      }
    } catch (error) {
      alert("Error recording payment: " + error);
    }
  };

  if (!receivableData || !customers || !dashboardData) {
    return (
      <div className="flex h-screen">
        <Sidebar />
        <div className="flex-1 flex flex-col">
          <Navbar />
          <main className="p-6 bg-gray-100 flex-1 overflow-auto">
            <h1 className="text-3xl font-bold mb-6">Accounts Receivable</h1>
            <p className="text-gray-500">Loading...</p>
          </main>
        </div>
      </div>
    );
  }

  const { receivables, agingReport, summary } = receivableData;

  return (
    <div className="flex h-screen">
      <Sidebar />
      <div className="flex-1 flex flex-col">
        <Navbar />
        <main className="p-6 bg-gradient-to-l from-teal-50 to-gray-50 flex-1 overflow-auto">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-3xl font-bold">Accounts Receivable</h1>
            <button
              type="button"
              onClick={() => setShowNewInvoiceModal(true)}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Create Invoice
            </button>
          </div>

          {/* Tab Navigation */}
          <div className="flex space-x-4 mb-6">
            {[
              { key: 'overview', label: 'Overview' },
              { key: 'invoices', label: 'Invoices' },
              { key: 'customers', label: 'Customers' },
              { key: 'aging', label: 'Aging Report' }
            ].map((tab) => (
              <button
                key={tab.key}
                type="button"
                onClick={() => setActiveTab(tab.key as any)}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  activeTab === tab.key
                    ? 'bg-gradient from-teal-50 to-gray-50 text-teal-400 border-2 border-teal-300'
                    : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                }`}
              >{ /* color="bg-gray-50 border-teal-400 text-teal-500"*/}
              
                {tab.label}
              </button>
            ))}
          </div>

          {/* Overview Tab */}
          {activeTab === 'overview' && (
            <div className="space-y-6">
              {/* Summary Cards */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600 mb-1">Total Outstanding</p>
                      <p className="text-3xl font-bold text-blue-600">
                        ${summary.totalOutstanding.toLocaleString()}
                      </p>
                    </div>
                    <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                      <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                      </svg>
                    </div>
                  </div>
                </div>

                <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600 mb-1">Overdue Amount</p>
                      <p className="text-3xl font-bold text-red-600">
                        ${summary.overdueAmount.toLocaleString()}
                      </p>
                    </div>
                    <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                      <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                      </svg>
                    </div>
                  </div>
                </div>

                <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600 mb-1">Total Invoices</p>
                      <p className="text-3xl font-bold text-green-600">
                        {summary.totalInvoices}
                      </p>
                    </div>
                    <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                      <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                    </div>
                  </div>
                </div>

                <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600 mb-1">Avg Days Outstanding</p>
                      <p className="text-3xl font-bold text-purple-600">
                        {Math.round(summary.averageDaysOutstanding)}
                      </p>
                    </div>
                    <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                      <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>

              {/* Recent Invoices */}
              <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">Recent Outstanding Invoices</h3>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b border-gray-200">
                        <th className="text-left py-3 px-4 font-semibold text-gray-700">Invoice #</th>
                        <th className="text-left py-3 px-4 font-semibold text-gray-700">Customer</th>
                        <th className="text-left py-3 px-4 font-semibold text-gray-700">Due Date</th>
                        <th className="text-right py-3 px-4 font-semibold text-gray-700">Amount</th>
                        <th className="text-center py-3 px-4 font-semibold text-gray-700">Status</th>
                        <th className="text-center py-3 px-4 font-semibold text-gray-700">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {receivables.slice(0, 5).map((invoice) => (
                        <tr key={invoice._id} className="border-b border-gray-100 hover:bg-gray-50">
                          <td className="py-4 px-4 font-medium text-blue-600">{invoice.invoiceNumber}</td>
                          <td className="py-4 px-4">{invoice.customerName}</td>
                          <td className="py-4 px-4">
                            <span className={`${
                              invoice.daysPastDue > 0 ? 'text-red-600' : 'text-gray-600'
                            }`}>
                              {new Date(invoice.dueDate).toLocaleDateString()}
                            </span>
                          </td>
                          <td className="py-4 px-4 text-right font-medium">
                            ${invoice.outstandingAmount.toLocaleString()}
                          </td>
                          <td className="py-4 px-4 text-center">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              invoice.status === 'Paid' ? 'bg-green-100 text-green-800' :
                              invoice.status === 'Overdue' ? 'bg-red-100 text-red-800' :
                              'bg-yellow-100 text-yellow-800'
                            }`}>
                              {invoice.status}
                            </span>
                          </td>
                          <td className="py-4 px-4 text-center">
                            <button
                              type="button"
                              onClick={() => {
                                setSelectedInvoice(invoice);
                                setShowPaymentModal(true);
                              }}
                              className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                            >
                              Record Payment
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}

          {/* Invoices Tab */}
          {activeTab === 'invoices' && (
            <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">All Customer Invoices</h3>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="text-left py-3 px-4 font-semibold text-gray-700">Invoice #</th>
                      <th className="text-left py-3 px-4 font-semibold text-gray-700">Customer</th>
                      <th className="text-left py-3 px-4 font-semibold text-gray-700">Issue Date</th>
                      <th className="text-left py-3 px-4 font-semibold text-gray-700">Due Date</th>
                      <th className="text-right py-3 px-4 font-semibold text-gray-700">Total Amount</th>
                      <th className="text-right py-3 px-4 font-semibold text-gray-700">Outstanding</th>
                      <th className="text-center py-3 px-4 font-semibold text-gray-700">Status</th>
                      <th className="text-center py-3 px-4 font-semibold text-gray-700">Days Past Due</th>
                    </tr>
                  </thead>
                  <tbody>
                    {receivables.map((invoice) => (
                      <tr key={invoice._id} className="border-b border-gray-100 hover:bg-gray-50">
                        <td className="py-4 px-4 font-medium text-blue-600">{invoice.invoiceNumber}</td>
                        <td className="py-4 px-4">
                          <div>
                            <div className="font-medium">{invoice.customerName}</div>
                            <div className="text-sm text-gray-500">{invoice.customerType}</div>
                          </div>
                        </td>
                        <td className="py-4 px-4">{new Date(invoice.invoiceDate).toLocaleDateString()}</td>
                        <td className="py-4 px-4">
                          <span className={`${
                            invoice.daysPastDue > 0 ? 'text-red-600 font-medium' : 'text-gray-600'
                          }`}>
                            {new Date(invoice.dueDate).toLocaleDateString()}
                          </span>
                        </td>
                        <td className="py-4 px-4 text-right font-medium">
                          ${invoice.totalAmount.toLocaleString()}
                        </td>
                        <td className="py-4 px-4 text-right font-medium">
                          ${invoice.outstandingAmount.toLocaleString()}
                        </td>
                        <td className="py-4 px-4 text-center">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            invoice.status === 'Paid' ? 'bg-green-100 text-green-800' :
                            invoice.status === 'Overdue' ? 'bg-red-100 text-red-800' :
                            'bg-yellow-100 text-yellow-800'
                          }`}>
                            {invoice.status}
                          </span>
                        </td>
                        <td className="py-4 px-4 text-center">
                          <span className={`font-medium ${
                            invoice.daysPastDue > 30 ? 'text-red-600' :
                            invoice.daysPastDue > 0 ? 'text-orange-600' :
                            'text-gray-600'
                          }`}>
                            {invoice.daysPastDue > 0 ? invoice.daysPastDue : '-'}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {/* Customers Tab */}
          {activeTab === 'customers' && (
            <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">Customer Management</h3>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="text-left py-3 px-4 font-semibold text-gray-700">Customer Name</th>
                      <th className="text-left py-3 px-4 font-semibold text-gray-700">Type</th>
                      <th className="text-left py-3 px-4 font-semibold text-gray-700">Contact</th>
                      <th className="text-right py-3 px-4 font-semibold text-gray-700">Credit Limit</th>
                      <th className="text-left py-3 px-4 font-semibold text-gray-700">Payment Terms</th>
                      <th className="text-right py-3 px-4 font-semibold text-gray-700">Outstanding Balance</th>
                    </tr>
                  </thead>
                  <tbody>
                    {customers.map((customer) => {
                      const customerBalance = receivables
                        .filter(inv => inv.customerId === customer._id)
                        .reduce((sum, inv) => sum + inv.outstandingAmount, 0);

                      return (
                        <tr key={customer._id} className="border-b border-gray-100 hover:bg-gray-50">
                          <td className="py-4 px-4 font-medium">{customer.name}</td>
                          <td className="py-4 px-4">
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              {customer.customerType || 'Standard'}
                            </span>
                          </td>
                          <td className="py-4 px-4">
                            <div>
                              <div className="text-sm">{customer.contactEmail}</div>
                              {customer.phone && <div className="text-xs text-gray-500">{customer.phone}</div>}
                            </div>
                          </td>
                          <td className="py-4 px-4 text-right">
                            ${(customer.creditLimit || 0).toLocaleString()}
                          </td>
                          <td className="py-4 px-4">{customer.paymentTerms || 'Net 30'}</td>
                          <td className="py-4 px-4 text-right font-medium">
                            <span className={customerBalance > 0 ? 'text-red-600' : 'text-green-600'}>
                              ${customerBalance.toLocaleString()}
                            </span>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {/* Aging Report Tab */}
          {activeTab === 'aging' && (
            <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">Accounts Receivable Aging Report</h3>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="text-left py-3 px-4 font-semibold text-gray-700">Customer</th>
                      <th className="text-right py-3 px-4 font-semibold text-gray-700">Current</th>
                      <th className="text-right py-3 px-4 font-semibold text-gray-700">1-30 Days</th>
                      <th className="text-right py-3 px-4 font-semibold text-gray-700">31-60 Days</th>
                      <th className="text-right py-3 px-4 font-semibold text-gray-700">61-90 Days</th>
                      <th className="text-right py-3 px-4 font-semibold text-gray-700">90+ Days</th>
                      <th className="text-right py-3 px-4 font-semibold text-gray-700">Total</th>
                    </tr>
                  </thead>
                  <tbody>
                    {agingReport.map((customer) => (
                      <tr key={customer.customerId} className="border-b border-gray-100 hover:bg-gray-50">
                        <td className="py-4 px-4 font-medium">{customer.customerName}</td>
                        <td className="py-4 px-4 text-right">${customer.current.toLocaleString()}</td>
                        <td className="py-4 px-4 text-right text-yellow-600">${customer.days1to30.toLocaleString()}</td>
                        <td className="py-4 px-4 text-right text-orange-600">${customer.days31to60.toLocaleString()}</td>
                        <td className="py-4 px-4 text-right text-red-600">${customer.days61to90.toLocaleString()}</td>
                        <td className="py-4 px-4 text-right text-red-700 font-medium">${customer.days90Plus.toLocaleString()}</td>
                        <td className="py-4 px-4 text-right font-bold">${customer.total.toLocaleString()}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {/* Create Invoice Modal */}
          {showNewInvoiceModal && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
              <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
                <h3 className="text-lg font-semibold mb-4">Create New Invoice</h3>
                <form onSubmit={handleCreateInvoice}>
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Customer
                        </label>
                        <select
                          value={invoiceFormData.customerId}
                          onChange={(e) => setInvoiceFormData({...invoiceFormData, customerId: e.target.value})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          required
                        >
                          <option value="">Select Customer</option>
                          {customers?.map((customer) => (
                            <option key={customer._id} value={customer._id}>
                              {customer.name}
                            </option>
                          ))}
                        </select>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Invoice Number
                        </label>
                        <input
                          type="text"
                          value={invoiceFormData.invoiceNumber}
                          onChange={(e) => setInvoiceFormData({...invoiceFormData, invoiceNumber: e.target.value})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="INV-001"
                          required
                        />
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Invoice Date
                        </label>
                        <input
                          type="date"
                          value={invoiceFormData.invoiceDate}
                          onChange={(e) => setInvoiceFormData({...invoiceFormData, invoiceDate: e.target.value})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          required
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Due Date
                        </label>
                        <input
                          type="date"
                          value={invoiceFormData.dueDate}
                          onChange={(e) => setInvoiceFormData({...invoiceFormData, dueDate: e.target.value})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          required
                        />
                      </div>
                    </div>
                    <div className="grid grid-cols-3 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Total Amount
                        </label>
                        <input
                          type="number"
                          step="any"
                          value={invoiceFormData.totalAmount}
                          onChange={(e) => setInvoiceFormData({...invoiceFormData, totalAmount: Number(e.target.value)})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          min="0"
                          required
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Tax Amount
                        </label>
                        <input
                          type="number"
                          step="any"
                          value={invoiceFormData.taxAmount}
                          onChange={(e) => setInvoiceFormData({...invoiceFormData, taxAmount: Number(e.target.value)})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          min="0"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Discount Amount
                        </label>
                        <input
                          type="number"
                          step="0.01"
                          value={invoiceFormData.discountAmount}
                          onChange={(e) => setInvoiceFormData({...invoiceFormData, discountAmount: Number(e.target.value)})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          min="0"
                        />
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Sales Order ID (Optional)
                      </label>
                      <input
                        type="text"
                        value={invoiceFormData.salesOrderId}
                        onChange={(e) => setInvoiceFormData({...invoiceFormData, salesOrderId: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="SO-001"
                      />
                    </div>
                  </div>
                  <div className="flex justify-end space-x-3 mt-6">
                    <button
                      type="button"
                      onClick={() => {
                        setShowNewInvoiceModal(false);
                        setInvoiceFormData({
                          customerId: "",
                          invoiceNumber: "",
                          invoiceDate: new Date().toISOString().split('T')[0],
                          dueDate: "",
                          totalAmount: 0,
                          taxAmount: 0,
                          discountAmount: 0,
                          salesOrderId: ""
                        });
                      }}
                      className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                    >
                      Create Invoice
                    </button>
                  </div>
                </form>
              </div>
            </div>
          )}

          {/* Record Payment Modal */}
          {showPaymentModal && selectedInvoice && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
              <div className="bg-white rounded-lg p-6 w-full max-w-lg">
                <h3 className="text-lg font-semibold mb-4">Record Payment</h3>
                <div className="mb-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
                  <h4 className="font-semibold text-blue-800 mb-2">Invoice Details</h4>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <p className="text-gray-700"><span className="font-medium">Invoice:</span> {selectedInvoice.invoiceNumber}</p>
                    <p className="text-gray-700"><span className="font-medium">Customer:</span> {selectedInvoice.customerName}</p>
                    <p className="text-gray-700"><span className="font-medium">Total Amount:</span> ${selectedInvoice.totalAmount?.toLocaleString()}</p>
                    <p className="text-gray-700"><span className="font-medium">Paid Amount:</span> ${selectedInvoice.paidAmount?.toLocaleString()}</p>
                    <p className="text-red-600 font-semibold col-span-2">
                      <span className="font-medium">Outstanding:</span> ${selectedInvoice.outstandingAmount?.toLocaleString()}
                    </p>
                  </div>
                </div>
                <form onSubmit={handleRecordPayment}>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Payment Amount
                      </label>
                      <input
                        type="number"
                        step="any"
                        value={paymentFormData.amount}
                        onChange={(e) => setPaymentFormData({...paymentFormData, amount: Number(e.target.value)})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        min="0"
                        max={selectedInvoice.outstandingAmount}
                        required
                      />
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Payment Date
                        </label>
                        <input
                          type="date"
                          value={paymentFormData.paymentDate}
                          onChange={(e) => setPaymentFormData({...paymentFormData, paymentDate: e.target.value})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          required
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Payment Method
                        </label>
                        <select
                          value={paymentFormData.paymentMethod}
                          onChange={(e) => setPaymentFormData({...paymentFormData, paymentMethod: e.target.value})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                          <option value="Bank Transfer">Bank Transfer</option>
                          <option value="Check">Check</option>
                          <option value="Cash">Cash</option>
                          <option value="Credit Card">Credit Card</option>
                        </select>
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Reference (Optional)
                      </label>
                      <input
                        type="text"
                        value={paymentFormData.reference}
                        onChange={(e) => setPaymentFormData({...paymentFormData, reference: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="Check number, transaction ID, etc."
                      />
                    </div>
                  </div>
                  <div className="flex justify-end space-x-3 mt-6">
                    <button
                      type="button"
                      onClick={() => {
                        setShowPaymentModal(false);
                        setSelectedInvoice(null);
                        setPaymentFormData({
                          amount: 0,
                          paymentDate: new Date().toISOString().split('T')[0],
                          paymentMethod: "Bank Transfer",
                          reference: ""
                        });
                      }}
                      className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                    >
                      Record Payment
                    </button>
                  </div>
                </form>
              </div>
            </div>
          )}
        </main>
      </div>
    </div>
  );
}

export default AccountsReceivable;
