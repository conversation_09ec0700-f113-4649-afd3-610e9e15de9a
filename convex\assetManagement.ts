import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

// ============================================================================
// FIXED ASSET MANAGEMENT SYSTEM
// PAS 16 Compliant • Automated Depreciation • Asset Disposal Tracking
// ============================================================================

// Add Fixed Asset
export const addFixedAsset = mutation({
  args: {
    assetNumber: v.string(),
    assetName: v.string(),
    assetCategory: v.union(
      v.literal("Building"), v.literal("Machinery"), v.literal("Equipment"),
      v.literal("Furniture"), v.literal("Vehicles"), v.literal("Computer Equipment"),
      v.literal("Leasehold Improvements"), v.literal("Other")
    ),
    acquisitionDate: v.string(),
    acquisitionCost: v.number(),
    depreciationMethod: v.union(
      v.literal("Straight Line"), v.literal("Declining Balance"), 
      v.literal("Sum of Years Digits"), v.literal("Units of Production")
    ),
    usefulLife: v.number(),
    salvageValue: v.number(),
    location: v.optional(v.string()),
    departmentId: v.optional(v.string()),
    costCenterId: v.optional(v.string()),
    serialNumber: v.optional(v.string()),
    supplier: v.optional(v.string()),
    warrantyExpiry: v.optional(v.string())
  },
  handler: async (ctx, args) => {
    // Calculate monthly depreciation
    const depreciableAmount = args.acquisitionCost - args.salvageValue;
    const monthlyDepreciation = depreciableAmount / (args.usefulLife * 12);

    // Get asset and depreciation accounts
    const accounts = await ctx.db.query("accounts").collect();
    const assetAccount = accounts.find(acc => 
      acc.type === "Asset" && acc.subType === "Fixed Asset" && 
      acc.name.toLowerCase().includes(args.assetCategory.toLowerCase())
    );
    const depreciationAccount = accounts.find(acc => 
      acc.name.toLowerCase().includes("accumulated depreciation")
    );

    if (!assetAccount) {
      throw new Error(`Asset account for ${args.assetCategory} not found. Please create the appropriate fixed asset account.`);
    }

    // Create fixed asset record
    const assetId = await ctx.db.insert("fixedAssets", {
      assetNumber: args.assetNumber,
      assetName: args.assetName,
      assetCategory: args.assetCategory,
      acquisitionDate: args.acquisitionDate,
      acquisitionCost: args.acquisitionCost,
      accumulatedDepreciation: 0,
      bookValue: args.acquisitionCost,
      depreciationMethod: args.depreciationMethod,
      usefulLife: args.usefulLife,
      salvageValue: args.salvageValue,
      monthlyDepreciation: monthlyDepreciation,
      location: args.location,
      departmentId: args.departmentId,
      costCenterId: args.costCenterId,
      serialNumber: args.serialNumber,
      supplier: args.supplier,
      warrantyExpiry: args.warrantyExpiry,
      status: "Active",
      assetAccountId: assetAccount._id,
      depreciationAccountId: depreciationAccount?._id
    });

    // Get current fiscal period
    const currentPeriod = await ctx.db.query("fiscalPeriods")
      .filter(q => q.and(
        q.lte(q.field("startDate"), args.acquisitionDate),
        q.gte(q.field("endDate"), args.acquisitionDate),
        q.eq(q.field("status"), "Open")
      ))
      .first();

    if (!currentPeriod) {
      throw new Error("No open fiscal period found for the acquisition date");
    }

    // Create journal entry for asset acquisition
    const batchId = `FA-${args.assetNumber}`;
    
    // Debit: Fixed Asset Account
    await ctx.db.insert("journalEntries", {
      entryNumber: `${batchId}-001`,
      date: args.acquisitionDate,
      accountId: assetAccount._id,
      description: `Asset Acquisition - ${args.assetName}`,
      debit: args.acquisitionCost,
      credit: 0,
      reference: args.assetNumber,
      transactionType: "Purchase",
      departmentId: args.departmentId || "",
      costCenterId: args.costCenterId || "",
      fiscalPeriodId: currentPeriod._id,
      isPosted: true,
      postedBy: "System",
      postedDate: args.acquisitionDate,
      batchId: batchId
    });

    // Credit: Cash/Accounts Payable (assuming cash for now)
    const cashAccount = accounts.find(acc => acc.code === "111");
    if (cashAccount) {
      await ctx.db.insert("journalEntries", {
        entryNumber: `${batchId}-002`,
        date: args.acquisitionDate,
        accountId: cashAccount._id,
        description: `Asset Purchase - ${args.assetName}`,
        debit: 0,
        credit: args.acquisitionCost,
        reference: args.assetNumber,
        transactionType: "Purchase",
        departmentId: args.departmentId || "",
        costCenterId: args.costCenterId || "",
        fiscalPeriodId: currentPeriod._id,
        isPosted: true,
        postedBy: "System",
        postedDate: args.acquisitionDate,
        batchId: batchId
      });
    }

    return {
      success: true,
      assetId,
      message: `✅ Fixed asset added successfully\n\n📋 Asset Details:\n• Asset: ${args.assetName} (${args.assetNumber})\n• Cost: ₱${args.acquisitionCost.toLocaleString()}\n• Useful Life: ${args.usefulLife} years\n• Monthly Depreciation: ₱${monthlyDepreciation.toLocaleString()}\n• Batch ID: ${batchId}`,
      assetNumber: args.assetNumber,
      assetName: args.assetName,
      acquisitionCost: args.acquisitionCost,
      monthlyDepreciation: monthlyDepreciation,
      usefulLife: args.usefulLife,
      batchId,
      pas16Compliant: true
    };
  }
});

// Calculate Monthly Depreciation for All Assets
export const calculateMonthlyDepreciation = mutation({
  args: {
    periodDate: v.string() // YYYY-MM format
  },
  handler: async (ctx, args) => {
    const assets = await ctx.db.query("fixedAssets")
      .filter(q => q.eq(q.field("status"), "Active"))
      .collect();

    if (assets.length === 0) {
      return {
        success: true,
        message: "No active assets found for depreciation calculation",
        assetsDepreciated: 0,
        totalDepreciation: 0,
        period: args.periodDate
      };
    }

    // Get accounts
    const accounts = await ctx.db.query("accounts").collect();
    const depreciationExpenseAccount = accounts.find(acc => acc.code === "506" || acc.name.toLowerCase().includes("depreciation expense"));
    const accumulatedDepreciationAccount = accounts.find(acc => acc.name.toLowerCase().includes("accumulated depreciation"));

    if (!depreciationExpenseAccount || !accumulatedDepreciationAccount) {
      throw new Error("Required depreciation accounts not found. Please ensure accounts 506-Depreciation Expense and Accumulated Depreciation exist.");
    }

    // Get current fiscal period
    const periodYear = parseInt(args.periodDate.split('-')[0]);
    const periodMonth = parseInt(args.periodDate.split('-')[1]);
    const periodStartDate = `${args.periodDate}-01`;
    const periodEndDate = new Date(periodYear, periodMonth, 0).toISOString().split('T')[0];

    const currentPeriod = await ctx.db.query("fiscalPeriods")
      .filter(q => q.and(
        q.lte(q.field("startDate"), periodEndDate),
        q.gte(q.field("endDate"), periodStartDate)
      ))
      .first();

    if (!currentPeriod) {
      throw new Error("No fiscal period found for the depreciation period");
    }

    let totalDepreciation = 0;
    let assetsDepreciated = 0;
    const batchId = `DEP-${args.periodDate}`;

    for (const asset of assets) {
      // Check if asset was acquired before or during this period
      const acquisitionDate = new Date(asset.acquisitionDate);
      const periodDate = new Date(periodStartDate);
      
      if (acquisitionDate > periodDate) {
        continue; // Skip assets acquired after this period
      }

      // Check if depreciation already calculated for this period
      const existingDepreciation = await ctx.db.query("depreciationSchedule")
        .filter(q => q.and(
          q.eq(q.field("assetId"), asset._id),
          q.eq(q.field("periodDate"), args.periodDate)
        ))
        .first();

      if (existingDepreciation) {
        continue; // Skip if already calculated
      }

      // Calculate depreciation for this period
      let depreciationAmount = 0;
      const currentBookValue = asset.bookValue;

      if (currentBookValue > asset.salvageValue) {
        switch (asset.depreciationMethod) {
          case "Straight Line":
            depreciationAmount = Math.min(asset.monthlyDepreciation, currentBookValue - asset.salvageValue);
            break;
          case "Declining Balance":
            const rate = 2 / asset.usefulLife / 12; // Double declining balance monthly rate
            depreciationAmount = Math.min(currentBookValue * rate, currentBookValue - asset.salvageValue);
            break;
          default:
            depreciationAmount = Math.min(asset.monthlyDepreciation, currentBookValue - asset.salvageValue);
        }
      }

      if (depreciationAmount > 0) {
        // Create depreciation schedule entry
        await ctx.db.insert("depreciationSchedule", {
          assetId: asset._id,
          periodDate: args.periodDate,
          openingBookValue: currentBookValue,
          depreciationExpense: depreciationAmount,
          accumulatedDepreciation: asset.accumulatedDepreciation + depreciationAmount,
          closingBookValue: currentBookValue - depreciationAmount,
          isPosted: true,
          fiscalPeriodId: currentPeriod._id
        });

        // Create journal entries
        // Debit: Depreciation Expense
        await ctx.db.insert("journalEntries", {
          entryNumber: `${batchId}-${asset.assetNumber}-001`,
          date: periodEndDate,
          accountId: depreciationExpenseAccount._id,
          description: `Monthly Depreciation - ${asset.assetName}`,
          debit: depreciationAmount,
          credit: 0,
          reference: asset.assetNumber,
          transactionType: "Depreciation",
          departmentId: asset.departmentId || "",
          costCenterId: asset.costCenterId || "",
          fiscalPeriodId: currentPeriod._id,
          isPosted: true,
          postedBy: "System",
          postedDate: periodEndDate,
          batchId: batchId
        });

        // Credit: Accumulated Depreciation
        await ctx.db.insert("journalEntries", {
          entryNumber: `${batchId}-${asset.assetNumber}-002`,
          date: periodEndDate,
          accountId: accumulatedDepreciationAccount._id,
          description: `Accumulated Depreciation - ${asset.assetName}`,
          debit: 0,
          credit: depreciationAmount,
          reference: asset.assetNumber,
          transactionType: "Depreciation",
          departmentId: asset.departmentId || "",
          costCenterId: asset.costCenterId || "",
          fiscalPeriodId: currentPeriod._id,
          isPosted: true,
          postedBy: "System",
          postedDate: periodEndDate,
          batchId: batchId
        });

        // Update asset record
        await ctx.db.patch(asset._id, {
          accumulatedDepreciation: asset.accumulatedDepreciation + depreciationAmount,
          bookValue: currentBookValue - depreciationAmount,
          lastDepreciationDate: periodEndDate
        });

        totalDepreciation += depreciationAmount;
        assetsDepreciated++;
      }
    }

    return {
      success: true,
      message: `✅ Monthly depreciation calculated successfully\n\n📋 PAS 16 Depreciation Summary:\n• Period: ${args.periodDate}\n• Assets Depreciated: ${assetsDepreciated}\n• Total Depreciation: ₱${totalDepreciation.toLocaleString()}\n• Batch ID: ${batchId}`,
      period: args.periodDate,
      assetsDepreciated,
      totalDepreciation,
      batchId,
      pas16Compliant: true
    };
  }
});

// Get Asset Depreciation Schedule
export const getAssetDepreciationSchedule = query({
  args: {
    assetId: v.optional(v.id("fixedAssets")),
    startDate: v.optional(v.string()),
    endDate: v.optional(v.string())
  },
  handler: async (ctx, args) => {
    let query = ctx.db.query("depreciationSchedule");
    
    if (args.assetId) {
      query = query.filter(q => q.eq(q.field("assetId"), args.assetId));
    }

    const scheduleEntries = await query.collect();

    // Filter by date range if provided
    const filteredEntries = scheduleEntries.filter(entry => {
      if (args.startDate && entry.periodDate < args.startDate) return false;
      if (args.endDate && entry.periodDate > args.endDate) return false;
      return true;
    });

    // Get asset details
    const assetIds = [...new Set(filteredEntries.map(entry => entry.assetId))];
    const assets = await Promise.all(assetIds.map(id => ctx.db.get(id)));
    const assetMap = new Map(assets.filter(Boolean).map(asset => [asset!._id, asset]));

    // Prepare schedule data
    const scheduleData = filteredEntries.map(entry => {
      const asset = assetMap.get(entry.assetId);
      return {
        assetNumber: asset?.assetNumber || "Unknown",
        assetName: asset?.assetName || "Unknown",
        assetCategory: asset?.assetCategory || "Unknown",
        periodDate: entry.periodDate,
        openingBookValue: entry.openingBookValue,
        depreciationExpense: entry.depreciationExpense,
        accumulatedDepreciation: entry.accumulatedDepreciation,
        closingBookValue: entry.closingBookValue,
        isPosted: entry.isPosted,
        depreciationRate: asset ? (entry.depreciationExpense / entry.openingBookValue) * 100 : 0
      };
    }).sort((a, b) => a.periodDate.localeCompare(b.periodDate));

    // Calculate summary
    const totalDepreciation = filteredEntries.reduce((sum, entry) => sum + entry.depreciationExpense, 0);
    const totalBookValue = filteredEntries.reduce((sum, entry) => sum + entry.closingBookValue, 0);

    return {
      reportTitle: "Asset Depreciation Schedule",
      companyName: "XYZ Manufacturing Ltd.",
      period: args.startDate && args.endDate ? 
        { startDate: args.startDate, endDate: args.endDate } : 
        "All Periods",
      currency: "PHP",
      scheduleData,
      summary: {
        totalEntries: filteredEntries.length,
        totalDepreciation,
        totalBookValue,
        assetsIncluded: assetIds.length
      },
      pas16Compliant: true,
      generatedAt: new Date().toISOString()
    };
  }
});

// Get Fixed Assets Summary
export const getFixedAssetsSummary = query({
  args: {
    asOfDate: v.optional(v.string()),
    category: v.optional(v.union(
      v.literal("Building"), v.literal("Machinery"), v.literal("Equipment"),
      v.literal("Furniture"), v.literal("Vehicles"), v.literal("Computer Equipment"),
      v.literal("Leasehold Improvements"), v.literal("Other")
    ))
  },
  handler: async (ctx, args) => {
    let query = ctx.db.query("fixedAssets");
    
    if (args.category) {
      query = query.filter(q => q.eq(q.field("assetCategory"), args.category));
    }

    const assets = await query.collect();
    const asOfDate = args.asOfDate || new Date().toISOString().split('T')[0];

    // Group by category
    const assetsByCategory = new Map<string, any[]>();
    assets.forEach(asset => {
      if (!assetsByCategory.has(asset.assetCategory)) {
        assetsByCategory.set(asset.assetCategory, []);
      }
      assetsByCategory.get(asset.assetCategory)!.push(asset);
    });

    // Calculate summary by category
    const categorySummary = Array.from(assetsByCategory.entries()).map(([category, categoryAssets]) => {
      const totalCost = categoryAssets.reduce((sum, asset) => sum + asset.acquisitionCost, 0);
      const totalDepreciation = categoryAssets.reduce((sum, asset) => sum + asset.accumulatedDepreciation, 0);
      const totalBookValue = categoryAssets.reduce((sum, asset) => sum + asset.bookValue, 0);
      const activeAssets = categoryAssets.filter(asset => asset.status === "Active").length;

      return {
        category,
        assetCount: categoryAssets.length,
        activeAssets,
        totalCost,
        totalDepreciation,
        totalBookValue,
        depreciationRate: totalCost > 0 ? (totalDepreciation / totalCost) * 100 : 0,
        averageAge: categoryAssets.length > 0 ? 
          categoryAssets.reduce((sum, asset) => {
            const acquisitionDate = new Date(asset.acquisitionDate);
            const currentDate = new Date(asOfDate);
            const ageInYears = (currentDate.getTime() - acquisitionDate.getTime()) / (1000 * 60 * 60 * 24 * 365);
            return sum + ageInYears;
          }, 0) / categoryAssets.length : 0
      };
    });

    // Overall totals
    const overallTotals = {
      totalAssets: assets.length,
      activeAssets: assets.filter(asset => asset.status === "Active").length,
      totalCost: assets.reduce((sum, asset) => sum + asset.acquisitionCost, 0),
      totalDepreciation: assets.reduce((sum, asset) => sum + asset.accumulatedDepreciation, 0),
      totalBookValue: assets.reduce((sum, asset) => sum + asset.bookValue, 0),
      overallDepreciationRate: 0
    };
    overallTotals.overallDepreciationRate = overallTotals.totalCost > 0 ? 
      (overallTotals.totalDepreciation / overallTotals.totalCost) * 100 : 0;

    return {
      reportTitle: "Fixed Assets Summary",
      companyName: "XYZ Manufacturing Ltd.",
      asOfDate,
      currency: "PHP",
      categorySummary,
      overallTotals,
      assets: assets.map(asset => ({
        assetNumber: asset.assetNumber,
        assetName: asset.assetName,
        assetCategory: asset.assetCategory,
        acquisitionDate: asset.acquisitionDate,
        acquisitionCost: asset.acquisitionCost,
        accumulatedDepreciation: asset.accumulatedDepreciation,
        bookValue: asset.bookValue,
        monthlyDepreciation: asset.monthlyDepreciation,
        usefulLife: asset.usefulLife,
        status: asset.status,
        location: asset.location,
        depreciationRate: asset.acquisitionCost > 0 ? (asset.accumulatedDepreciation / asset.acquisitionCost) * 100 : 0
      })),
      pas16Compliant: true,
      generatedAt: new Date().toISOString()
    };
  }
});
