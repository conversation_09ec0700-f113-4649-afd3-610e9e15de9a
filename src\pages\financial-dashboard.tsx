import React, { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import Sidebar from "../components/sidebar";
import Navbar from "../components/navbar";

function FinancialDashboard() {
  const [activeTab, setActiveTab] = useState("revenue-tracking");
  const [dateRange, setDateRange] = useState({
    startDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0]
  });

  // Queries for financial data
  const revenueData = useQuery(api.financialCore.getRevenueTrackingSummary, dateRange);
  const expenseData = useQuery(api.financialCore.getExpenseManagementSummary, dateRange);
  const trialBalance = useQuery(api.financialReporting.getTrialBalance, {
    asOfDate: dateRange.endDate
  });
  const statementOfFinancialPosition = useQuery(api.financialReporting.getStatementOfFinancialPosition, {
    asOfDate: dateRange.endDate
  });
  const incomeStatement = useQuery(api.financialReporting.getStatementOfComprehensiveIncome, dateRange);

  // Mutations
  const recordSalesRevenue = useMutation(api.financialCore.recordSalesRevenue);
  const recordPurchaseExpense = useMutation(api.financialCore.recordPurchaseExpense);

  if (!revenueData || !expenseData || !trialBalance) {
    return (
      <div className="flex h-screen bg-gradient-to-br from-slate-50 to-blue-50">
        <Sidebar />
        <div className="flex-1 flex flex-col">
          <Navbar />
          <main className="p-8 flex-1 overflow-auto">
            <div className="max-w-7xl mx-auto">
              <div className="text-center py-16">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-6">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>
                <h1 className="text-3xl font-bold text-gray-900 mb-4">Financial Dashboard</h1>
                <p className="text-lg text-gray-600">Loading financial data...</p>
              </div>
            </div>
          </main>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      <Sidebar />
      <div className="flex-1 flex flex-col">
        <Navbar />
        <main className="p-8 flex-1 overflow-auto">
          <div className="max-w-7xl mx-auto">
            {/* Header */}
            <div className="mb-8">
              <h1 className="text-4xl font-bold text-gray-900 mb-2">
                Financial Dashboard
              </h1>
              <p className="text-lg text-gray-600">
                Philippine GAAP Compliant • Revenue Tracking • Expense Management • Financial Reporting
              </p>
            </div>

            {/* Date Range Selector */}
            <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6 mb-8">
              <div className="flex items-center space-x-4">
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">Start Date</label>
                  <input
                    type="date"
                    value={dateRange.startDate}
                    onChange={(e) => setDateRange({...dateRange, startDate: e.target.value})}
                    className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">End Date</label>
                  <input
                    type="date"
                    value={dateRange.endDate}
                    onChange={(e) => setDateRange({...dateRange, endDate: e.target.value})}
                    className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>
            </div>

            {/* Navigation Tabs */}
            <div className="flex flex-wrap gap-3 mb-8">
              <button
                type="button"
                onClick={() => setActiveTab("revenue-tracking")}
                className={`inline-flex items-center px-6 py-3 rounded-xl font-semibold transition-all duration-200 shadow-sm ${
                  activeTab === "revenue-tracking"
                    ? "bg-green-600 text-white shadow-lg shadow-green-200 transform scale-105"
                    : "bg-white text-gray-700 border border-gray-200 hover:bg-green-50 hover:border-green-300"
                }`}
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                </svg>
                Revenue Tracking
              </button>
              <button
                type="button"
                onClick={() => setActiveTab("expense-management")}
                className={`inline-flex items-center px-6 py-3 rounded-xl font-semibold transition-all duration-200 shadow-sm ${
                  activeTab === "expense-management"
                    ? "bg-red-600 text-white shadow-lg shadow-red-200 transform scale-105"
                    : "bg-white text-gray-700 border border-gray-200 hover:bg-red-50 hover:border-red-300"
                }`}
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                Expense Management
              </button>
              <button
                type="button"
                onClick={() => setActiveTab("financial-reports")}
                className={`inline-flex items-center px-6 py-3 rounded-xl font-semibold transition-all duration-200 shadow-sm ${
                  activeTab === "financial-reports"
                    ? "bg-blue-600 text-white shadow-lg shadow-blue-200 transform scale-105"
                    : "bg-white text-gray-700 border border-gray-200 hover:bg-blue-50 hover:border-blue-300"
                }`}
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                Financial Reports
              </button>
            </div>

            {/* Revenue Tracking Tab */}
            {activeTab === "revenue-tracking" && (
              <div className="space-y-8">
                {/* Revenue Summary Cards */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                  <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-xl p-6 border border-green-200">
                    <div className="text-3xl font-bold text-green-700 mb-2">₱{revenueData.summary.totalInvoiced.toLocaleString()}</div>
                    <div className="text-sm font-medium text-green-600">Total Invoiced</div>
                  </div>
                  <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-6 border border-blue-200">
                    <div className="text-3xl font-bold text-blue-700 mb-2">₱{revenueData.summary.totalCollected.toLocaleString()}</div>
                    <div className="text-sm font-medium text-blue-600">Total Collected</div>
                  </div>
                  <div className="bg-gradient-to-br from-orange-50 to-orange-100 rounded-xl p-6 border border-orange-200">
                    <div className="text-3xl font-bold text-orange-700 mb-2">₱{revenueData.summary.totalOutstanding.toLocaleString()}</div>
                    <div className="text-sm font-medium text-orange-600">Outstanding</div>
                  </div>
                  <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl p-6 border border-purple-200">
                    <div className="text-3xl font-bold text-purple-700 mb-2">{revenueData.summary.collectionRate.toFixed(1)}%</div>
                    <div className="text-sm font-medium text-purple-600">Collection Rate</div>
                  </div>
                </div>

                {/* PFRS 15 Revenue Recognition */}
                <div className="bg-white rounded-2xl shadow-xl border border-gray-200 p-8">
                  <h3 className="text-xl font-bold text-gray-900 mb-6">PFRS 15 Revenue Recognition Status</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    <div className="bg-gray-50 rounded-xl p-6">
                      <div className="text-lg font-semibold text-gray-700 mb-2">Recognized Revenue</div>
                      <div className="text-2xl font-bold text-green-600">₱{revenueData.pfrs15Metrics.totalRecognizedRevenue.toLocaleString()}</div>
                    </div>
                    <div className="bg-gray-50 rounded-xl p-6">
                      <div className="text-lg font-semibold text-gray-700 mb-2">Deferred Revenue</div>
                      <div className="text-2xl font-bold text-orange-600">₱{revenueData.pfrs15Metrics.totalDeferredRevenue.toLocaleString()}</div>
                    </div>
                    <div className="bg-gray-50 rounded-xl p-6">
                      <div className="text-lg font-semibold text-gray-700 mb-2">Recognition Rate</div>
                      <div className="text-2xl font-bold text-blue-600">{revenueData.pfrs15Metrics.recognitionRate.toFixed(1)}%</div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-700">{revenueData.pfrs15Metrics.statusBreakdown.fullyRecognized}</div>
                      <div className="text-sm text-green-600">Fully Recognized</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-yellow-700">{revenueData.pfrs15Metrics.statusBreakdown.partiallyRecognized}</div>
                      <div className="text-sm text-yellow-600">Partially Recognized</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-red-700">{revenueData.pfrs15Metrics.statusBreakdown.notRecognized}</div>
                      <div className="text-sm text-red-600">Not Recognized</div>
                    </div>
                  </div>
                </div>

                {/* Accounts Receivable Aging */}
                <div className="bg-white rounded-2xl shadow-xl border border-gray-200 p-8">
                  <h3 className="text-xl font-bold text-gray-900 mb-6">Accounts Receivable Aging Analysis</h3>
                  <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                    <div className="text-center">
                      <div className="text-lg font-bold text-green-700">₱{revenueData.agingAnalysis.current.toLocaleString()}</div>
                      <div className="text-sm text-green-600">Current</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-yellow-700">₱{revenueData.agingAnalysis.days30.toLocaleString()}</div>
                      <div className="text-sm text-yellow-600">1-30 Days</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-orange-700">₱{revenueData.agingAnalysis.days60.toLocaleString()}</div>
                      <div className="text-sm text-orange-600">31-60 Days</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-red-700">₱{revenueData.agingAnalysis.days90.toLocaleString()}</div>
                      <div className="text-sm text-red-600">61-90 Days</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-red-800">₱{revenueData.agingAnalysis.over90.toLocaleString()}</div>
                      <div className="text-sm text-red-700">Over 90 Days</div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Expense Management Tab */}
            {activeTab === "expense-management" && (
              <div className="space-y-8">
                {/* Expense Summary Cards */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                  <div className="bg-gradient-to-br from-red-50 to-red-100 rounded-xl p-6 border border-red-200">
                    <div className="text-3xl font-bold text-red-700 mb-2">₱{expenseData.summary.totalExpenses.toLocaleString()}</div>
                    <div className="text-sm font-medium text-red-600">Total Expenses</div>
                  </div>
                  <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-xl p-6 border border-green-200">
                    <div className="text-3xl font-bold text-green-700 mb-2">₱{expenseData.summary.totalPaid.toLocaleString()}</div>
                    <div className="text-sm font-medium text-green-600">Total Paid</div>
                  </div>
                  <div className="bg-gradient-to-br from-orange-50 to-orange-100 rounded-xl p-6 border border-orange-200">
                    <div className="text-3xl font-bold text-orange-700 mb-2">₱{expenseData.summary.totalOutstanding.toLocaleString()}</div>
                    <div className="text-sm font-medium text-orange-600">Outstanding Payables</div>
                  </div>
                  <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-6 border border-blue-200">
                    <div className="text-3xl font-bold text-blue-700 mb-2">₱{expenseData.summary.totalInputVAT.toLocaleString()}</div>
                    <div className="text-sm font-medium text-blue-600">Input VAT</div>
                  </div>
                </div>

                {/* BIR Tax Compliance */}
                <div className="bg-white rounded-2xl shadow-xl border border-gray-200 p-8">
                  <h3 className="text-xl font-bold text-gray-900 mb-6">BIR Tax Compliance Summary</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="bg-blue-50 rounded-xl p-6">
                      <div className="text-lg font-semibold text-blue-700 mb-2">Input VAT (Creditable)</div>
                      <div className="text-2xl font-bold text-blue-800">₱{expenseData.summary.totalInputVAT.toLocaleString()}</div>
                      <div className="text-sm text-blue-600 mt-2">12% VAT on purchases</div>
                    </div>
                    <div className="bg-green-50 rounded-xl p-6">
                      <div className="text-lg font-semibold text-green-700 mb-2">Withholding Tax</div>
                      <div className="text-2xl font-bold text-green-800">₱{expenseData.summary.totalWithholdingTax.toLocaleString()}</div>
                      <div className="text-sm text-green-600 mt-2">Creditable against income tax</div>
                    </div>
                    <div className="bg-purple-50 rounded-xl p-6">
                      <div className="text-lg font-semibold text-purple-700 mb-2">Payment Rate</div>
                      <div className="text-2xl font-bold text-purple-800">{expenseData.summary.paymentRate.toFixed(1)}%</div>
                      <div className="text-sm text-purple-600 mt-2">Expenses paid on time</div>
                    </div>
                  </div>
                </div>

                {/* Accounts Payable Aging */}
                <div className="bg-white rounded-2xl shadow-xl border border-gray-200 p-8">
                  <h3 className="text-xl font-bold text-gray-900 mb-6">Accounts Payable Aging Analysis</h3>
                  <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                    <div className="text-center">
                      <div className="text-lg font-bold text-green-700">₱{expenseData.payableAging.current.toLocaleString()}</div>
                      <div className="text-sm text-green-600">Current</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-yellow-700">₱{expenseData.payableAging.days30.toLocaleString()}</div>
                      <div className="text-sm text-yellow-600">1-30 Days</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-orange-700">₱{expenseData.payableAging.days60.toLocaleString()}</div>
                      <div className="text-sm text-orange-600">31-60 Days</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-red-700">₱{expenseData.payableAging.days90.toLocaleString()}</div>
                      <div className="text-sm text-red-600">61-90 Days</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-red-800">₱{expenseData.payableAging.over90.toLocaleString()}</div>
                      <div className="text-sm text-red-700">Over 90 Days</div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Financial Reports Tab */}
            {activeTab === "financial-reports" && (
              <div className="space-y-8">
                {/* Report Selection */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
                    <h3 className="text-lg font-bold text-gray-900 mb-4">Trial Balance</h3>
                    <p className="text-gray-600 text-sm mb-4">Philippine GAAP compliant trial balance with all account balances</p>
                    <div className="text-sm text-gray-500">
                      <div>Total Accounts: {trialBalance.accounts.length}</div>
                      <div>Balanced: {trialBalance.totals.isBalanced ? "✅ Yes" : "❌ No"}</div>
                    </div>
                  </div>
                  
                  {statementOfFinancialPosition && (
                    <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
                      <h3 className="text-lg font-bold text-gray-900 mb-4">Statement of Financial Position</h3>
                      <p className="text-gray-600 text-sm mb-4">PFRS compliant balance sheet showing assets, liabilities, and equity</p>
                      <div className="text-sm text-gray-500">
                        <div>Total Assets: ₱{statementOfFinancialPosition.assets.totalAssets.toLocaleString()}</div>
                        <div>Balanced: {statementOfFinancialPosition.balanceCheck ? "✅ Yes" : "❌ No"}</div>
                      </div>
                    </div>
                  )}

                  {incomeStatement && (
                    <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
                      <h3 className="text-lg font-bold text-gray-900 mb-4">Statement of Comprehensive Income</h3>
                      <p className="text-gray-600 text-sm mb-4">PFRS compliant income statement with comprehensive income</p>
                      <div className="text-sm text-gray-500">
                        <div>Revenue: ₱{incomeStatement.income.revenue.totalRevenue.toLocaleString()}</div>
                        <div>Net Income: ₱{incomeStatement.income.netIncome.toLocaleString()}</div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Key Financial Metrics */}
                {incomeStatement && statementOfFinancialPosition && (
                  <div className="bg-white rounded-2xl shadow-xl border border-gray-200 p-8">
                    <h3 className="text-xl font-bold text-gray-900 mb-6">Key Financial Metrics</h3>
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-blue-700">{incomeStatement.keyRatios.grossProfitMargin.toFixed(1)}%</div>
                        <div className="text-sm text-blue-600">Gross Profit Margin</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-700">{incomeStatement.keyRatios.operatingMargin.toFixed(1)}%</div>
                        <div className="text-sm text-green-600">Operating Margin</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-purple-700">{incomeStatement.keyRatios.netProfitMargin.toFixed(1)}%</div>
                        <div className="text-sm text-purple-600">Net Profit Margin</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-orange-700">
                          {statementOfFinancialPosition.assets.totalAssets > 0 ? 
                            ((incomeStatement.income.netIncome / statementOfFinancialPosition.assets.totalAssets) * 100).toFixed(1) : 0}%
                        </div>
                        <div className="text-sm text-orange-600">Return on Assets</div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </main>
      </div>
    </div>
  );
}

export default FinancialDashboard;
