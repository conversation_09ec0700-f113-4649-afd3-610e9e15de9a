import Sidebar from "../components/sidebar";
import Navbar from "../components/navbar";

function Users() {
  return (
    <div className="flex h-screen">
      <Sidebar />
      <div className="flex-1 flex flex-col">
        <Navbar />
        <main className="p-6 bg-gray-100 flex-1 overflow-auto">
          <h1 className="text-3xl font-bold mb-6">User Management</h1>
          <div className="bg-white p-8 rounded-lg shadow-md">
            <p className="text-gray-600">
              This page will manage system users, roles, and permissions.
            </p>
            <div className="mt-4 text-sm text-gray-500">
              Features to be implemented:
              <ul className="list-disc list-inside mt-2">
                <li>View all system users</li>
                <li>Add new users</li>
                <li>Edit user information</li>
                <li>Assign roles and permissions</li>
                <li>Deactivate users</li>
                <li>Audit user activity</li>
              </ul>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}

export default Users;
