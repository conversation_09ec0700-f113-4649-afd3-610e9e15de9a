import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

// ============================================================================
// FISCAL PERIOD MANAGEMENT SYSTEM
// Period Closing • Opening Balances • Year-End Processing
// ============================================================================

// Create Fiscal Period
export const createFiscalPeriod = mutation({
  args: {
    periodName: v.string(),
    periodType: v.union(v.literal("Monthly"), v.literal("Quarterly"), v.literal("Yearly")),
    startDate: v.string(),
    endDate: v.string(),
    fiscalYear: v.number(),
    quarter: v.optional(v.number()),
    month: v.optional(v.number()),
    notes: v.optional(v.string())
  },
  handler: async (ctx, args) => {
    // Check for overlapping periods
    const existingPeriods = await ctx.db.query("fiscalPeriods")
      .filter(q => q.or(
        q.and(
          q.lte(q.field("startDate"), args.startDate),
          q.gte(q.field("endDate"), args.startDate)
        ),
        q.and(
          q.lte(q.field("startDate"), args.endDate),
          q.gte(q.field("endDate"), args.endDate)
        )
      ))
      .collect();

    if (existingPeriods.length > 0) {
      throw new Error(`Fiscal period overlaps with existing period: ${existingPeriods[0].periodName}`);
    }

    const fiscalPeriodId = await ctx.db.insert("fiscalPeriods", {
      periodName: args.periodName,
      periodType: args.periodType,
      startDate: args.startDate,
      endDate: args.endDate,
      status: "Open",
      fiscalYear: args.fiscalYear,
      quarter: args.quarter,
      month: args.month,
      notes: args.notes
    });

    return {
      success: true,
      fiscalPeriodId,
      message: `✅ Fiscal period created successfully\n\n📋 Period Details:\n• Name: ${args.periodName}\n• Type: ${args.periodType}\n• Start Date: ${args.startDate}\n• End Date: ${args.endDate}\n• Status: Open`,
      periodName: args.periodName,
      periodType: args.periodType,
      startDate: args.startDate,
      endDate: args.endDate,
      status: "Open"
    };
  }
});

// Close Fiscal Period
export const closeFiscalPeriod = mutation({
  args: {
    fiscalPeriodId: v.id("fiscalPeriods"),
    closedBy: v.string(),
    notes: v.optional(v.string())
  },
  handler: async (ctx, args) => {
    const fiscalPeriod = await ctx.db.get(args.fiscalPeriodId);
    if (!fiscalPeriod) {
      throw new Error("Fiscal period not found");
    }

    if (fiscalPeriod.status !== "Open") {
      throw new Error("Only open fiscal periods can be closed");
    }

    // Get all accounts
    const accounts = await ctx.db.query("accounts").collect();

    // Calculate closing balances for all accounts
    const journalEntries = await ctx.db.query("journalEntries")
      .filter(q => q.and(
        q.gte(q.field("date"), fiscalPeriod.startDate),
        q.lte(q.field("date"), fiscalPeriod.endDate),
        q.eq(q.field("isPosted"), true)
      ))
      .collect();

    // Group journal entries by account
    const accountTransactions = new Map<string, { debits: number; credits: number }>();
    
    journalEntries.forEach(entry => {
      if (!accountTransactions.has(entry.accountId)) {
        accountTransactions.set(entry.accountId, { debits: 0, credits: 0 });
      }
      const transactions = accountTransactions.get(entry.accountId)!;
      transactions.debits += entry.debit;
      transactions.credits += entry.credit;
    });

    // Create or update account balances for this period
    for (const account of accounts) {
      const transactions = accountTransactions.get(account._id) || { debits: 0, credits: 0 };
      
      // Get opening balance (from previous period or account opening balance)
      let openingBalance = 0;
      const previousPeriod = await ctx.db.query("fiscalPeriods")
        .filter(q => q.and(
          q.lt(q.field("endDate"), fiscalPeriod.startDate),
          q.eq(q.field("status"), "Closed")
        ))
        .order("desc")
        .first();

      if (previousPeriod) {
        const previousBalance = await ctx.db.query("accountBalances")
          .filter(q => q.and(
            q.eq(q.field("accountId"), account._id),
            q.eq(q.field("fiscalPeriodId"), previousPeriod._id)
          ))
          .first();
        openingBalance = previousBalance?.closingBalance || 0;
      } else {
        openingBalance = account.openingBalance || 0;
      }

      const closingBalance = openingBalance + transactions.debits - transactions.credits;

      // Check if account balance already exists for this period
      const existingBalance = await ctx.db.query("accountBalances")
        .filter(q => q.and(
          q.eq(q.field("accountId"), account._id),
          q.eq(q.field("fiscalPeriodId"), args.fiscalPeriodId)
        ))
        .first();

      if (existingBalance) {
        await ctx.db.patch(existingBalance._id, {
          openingBalance,
          totalDebits: transactions.debits,
          totalCredits: transactions.credits,
          closingBalance,
          lastUpdated: new Date().toISOString()
        });
      } else {
        await ctx.db.insert("accountBalances", {
          accountId: account._id,
          fiscalPeriodId: args.fiscalPeriodId,
          openingBalance,
          totalDebits: transactions.debits,
          totalCredits: transactions.credits,
          closingBalance,
          lastUpdated: new Date().toISOString()
        });
      }
    }

    // Close the fiscal period
    await ctx.db.patch(args.fiscalPeriodId, {
      status: "Closed",
      closedBy: args.closedBy,
      closedDate: new Date().toISOString(),
      notes: args.notes
    });

    return {
      success: true,
      message: `✅ Fiscal period closed successfully\n\n📋 Closing Summary:\n• Period: ${fiscalPeriod.periodName}\n• Closed By: ${args.closedBy}\n• Accounts Processed: ${accounts.length}\n• Total Transactions: ${journalEntries.length}\n• Status: Closed`,
      periodName: fiscalPeriod.periodName,
      closedBy: args.closedBy,
      accountsProcessed: accounts.length,
      totalTransactions: journalEntries.length,
      status: "Closed"
    };
  }
});

// Reopen Fiscal Period
export const reopenFiscalPeriod = mutation({
  args: {
    fiscalPeriodId: v.id("fiscalPeriods"),
    reopenedBy: v.string(),
    reason: v.string()
  },
  handler: async (ctx, args) => {
    const fiscalPeriod = await ctx.db.get(args.fiscalPeriodId);
    if (!fiscalPeriod) {
      throw new Error("Fiscal period not found");
    }

    if (fiscalPeriod.status !== "Closed") {
      throw new Error("Only closed fiscal periods can be reopened");
    }

    // Check if there are subsequent closed periods
    const subsequentPeriods = await ctx.db.query("fiscalPeriods")
      .filter(q => q.and(
        q.gt(q.field("startDate"), fiscalPeriod.endDate),
        q.eq(q.field("status"), "Closed")
      ))
      .collect();

    if (subsequentPeriods.length > 0) {
      throw new Error("Cannot reopen period with subsequent closed periods. Please reopen periods in reverse chronological order.");
    }

    await ctx.db.patch(args.fiscalPeriodId, {
      status: "Open",
      closedBy: undefined,
      closedDate: undefined,
      notes: `Reopened by ${args.reopenedBy} on ${new Date().toISOString()}. Reason: ${args.reason}`
    });

    return {
      success: true,
      message: `✅ Fiscal period reopened successfully\n\n📋 Reopen Details:\n• Period: ${fiscalPeriod.periodName}\n• Reopened By: ${args.reopenedBy}\n• Reason: ${args.reason}\n• Status: Open`,
      periodName: fiscalPeriod.periodName,
      reopenedBy: args.reopenedBy,
      reason: args.reason,
      status: "Open"
    };
  }
});

// Get Fiscal Periods
export const getFiscalPeriods = query({
  args: {
    fiscalYear: v.optional(v.number()),
    status: v.optional(v.union(v.literal("Open"), v.literal("Closed"), v.literal("Locked")))
  },
  handler: async (ctx, args) => {
    let query = ctx.db.query("fiscalPeriods");
    
    if (args.fiscalYear) {
      query = query.filter(q => q.eq(q.field("fiscalYear"), args.fiscalYear));
    }
    
    if (args.status) {
      query = query.filter(q => q.eq(q.field("status"), args.status));
    }

    const fiscalPeriods = await query.order("asc").collect();

    // Get account balance summaries for each period
    const periodsWithSummary = await Promise.all(
      fiscalPeriods.map(async (period) => {
        const accountBalances = await ctx.db.query("accountBalances")
          .filter(q => q.eq(q.field("fiscalPeriodId"), period._id))
          .collect();

        const totalDebits = accountBalances.reduce((sum, balance) => sum + balance.totalDebits, 0);
        const totalCredits = accountBalances.reduce((sum, balance) => sum + balance.totalCredits, 0);
        const accountsWithActivity = accountBalances.filter(balance => 
          balance.totalDebits > 0 || balance.totalCredits > 0
        ).length;

        return {
          fiscalPeriodId: period._id,
          periodName: period.periodName,
          periodType: period.periodType,
          startDate: period.startDate,
          endDate: period.endDate,
          status: period.status,
          fiscalYear: period.fiscalYear,
          quarter: period.quarter,
          month: period.month,
          closedBy: period.closedBy,
          closedDate: period.closedDate,
          notes: period.notes,
          summary: {
            totalDebits,
            totalCredits,
            accountsWithActivity,
            isBalanced: Math.abs(totalDebits - totalCredits) < 0.01
          }
        };
      })
    );

    // Calculate overall summary
    const totalPeriods = fiscalPeriods.length;
    const openPeriods = fiscalPeriods.filter(p => p.status === "Open").length;
    const closedPeriods = fiscalPeriods.filter(p => p.status === "Closed").length;
    const lockedPeriods = fiscalPeriods.filter(p => p.status === "Locked").length;

    return {
      reportTitle: "Fiscal Periods Report",
      companyName: "XYZ Manufacturing Ltd.",
      fiscalYear: args.fiscalYear,
      status: args.status,
      summary: {
        totalPeriods,
        openPeriods,
        closedPeriods,
        lockedPeriods
      },
      fiscalPeriods: periodsWithSummary,
      generatedAt: new Date().toISOString()
    };
  }
});

// Get Current Fiscal Period
export const getCurrentFiscalPeriod = query({
  args: {
    date: v.optional(v.string())
  },
  handler: async (ctx, args) => {
    const currentDate = args.date || new Date().toISOString().split('T')[0];
    
    const currentPeriod = await ctx.db.query("fiscalPeriods")
      .filter(q => q.and(
        q.lte(q.field("startDate"), currentDate),
        q.gte(q.field("endDate"), currentDate)
      ))
      .first();

    if (!currentPeriod) {
      return {
        currentPeriod: null,
        message: "No fiscal period found for the current date",
        currentDate,
        recommendation: "Please create a fiscal period that includes the current date"
      };
    }

    // Get account balances for current period
    const accountBalances = await ctx.db.query("accountBalances")
      .filter(q => q.eq(q.field("fiscalPeriodId"), currentPeriod._id))
      .collect();

    const totalDebits = accountBalances.reduce((sum, balance) => sum + balance.totalDebits, 0);
    const totalCredits = accountBalances.reduce((sum, balance) => sum + balance.totalCredits, 0);

    // Get recent transactions
    const recentTransactions = await ctx.db.query("journalEntries")
      .filter(q => q.and(
        q.eq(q.field("fiscalPeriodId"), currentPeriod._id),
        q.eq(q.field("isPosted"), true)
      ))
      .order("desc")
      .take(10);

    return {
      currentPeriod: {
        fiscalPeriodId: currentPeriod._id,
        periodName: currentPeriod.periodName,
        periodType: currentPeriod.periodType,
        startDate: currentPeriod.startDate,
        endDate: currentPeriod.endDate,
        status: currentPeriod.status,
        fiscalYear: currentPeriod.fiscalYear,
        quarter: currentPeriod.quarter,
        month: currentPeriod.month,
        daysRemaining: Math.ceil((new Date(currentPeriod.endDate).getTime() - new Date(currentDate).getTime()) / (1000 * 60 * 60 * 24)),
        summary: {
          totalDebits,
          totalCredits,
          isBalanced: Math.abs(totalDebits - totalCredits) < 0.01,
          accountsWithActivity: accountBalances.filter(balance => 
            balance.totalDebits > 0 || balance.totalCredits > 0
          ).length,
          totalTransactions: recentTransactions.length
        }
      },
      currentDate,
      recentTransactions: recentTransactions.map(entry => ({
        entryNumber: entry.entryNumber,
        date: entry.date,
        description: entry.description,
        debit: entry.debit,
        credit: entry.credit,
        reference: entry.reference,
        transactionType: entry.transactionType
      }))
    };
  }
});

// Create Year-End Closing Entries
export const createYearEndClosingEntries = mutation({
  args: {
    fiscalYear: v.number(),
    closingDate: v.string(),
    retainedEarningsAccountCode: v.string() // e.g., "310"
  },
  handler: async (ctx, args) => {
    // Get all revenue and expense accounts
    const accounts = await ctx.db.query("accounts").collect();
    const revenueAccounts = accounts.filter(acc => acc.type === "Revenue");
    const expenseAccounts = accounts.filter(acc => acc.type === "Expense");
    const retainedEarningsAccount = accounts.find(acc => acc.code === args.retainedEarningsAccountCode);

    if (!retainedEarningsAccount) {
      throw new Error(`Retained Earnings account with code ${args.retainedEarningsAccountCode} not found`);
    }

    // Get fiscal period for the closing date
    const fiscalPeriod = await ctx.db.query("fiscalPeriods")
      .filter(q => q.and(
        q.lte(q.field("startDate"), args.closingDate),
        q.gte(q.field("endDate"), args.closingDate),
        q.eq(q.field("fiscalYear"), args.fiscalYear)
      ))
      .first();

    if (!fiscalPeriod) {
      throw new Error("No fiscal period found for the closing date");
    }

    // Get year-to-date balances
    const yearStartDate = `${args.fiscalYear}-01-01`;
    const journalEntries = await ctx.db.query("journalEntries")
      .filter(q => q.and(
        q.gte(q.field("date"), yearStartDate),
        q.lte(q.field("date"), args.closingDate),
        q.eq(q.field("isPosted"), true)
      ))
      .collect();

    // Calculate account balances
    const accountBalances = new Map<string, number>();
    journalEntries.forEach(entry => {
      const currentBalance = accountBalances.get(entry.accountId) || 0;
      accountBalances.set(entry.accountId, currentBalance + entry.debit - entry.credit);
    });

    const batchId = `YEC-${args.fiscalYear}`;
    let entryCounter = 1;
    let totalRevenue = 0;
    let totalExpenses = 0;

    // Close Revenue Accounts (Debit Revenue, Credit Retained Earnings)
    for (const account of revenueAccounts) {
      const balance = accountBalances.get(account._id) || 0;
      const revenueAmount = Math.abs(balance); // Revenue accounts have credit balances
      
      if (revenueAmount > 0) {
        // Debit Revenue Account
        await ctx.db.insert("journalEntries", {
          entryNumber: `${batchId}-${String(entryCounter++).padStart(3, '0')}`,
          date: args.closingDate,
          accountId: account._id,
          description: `Year-End Closing - ${account.name}`,
          debit: revenueAmount,
          credit: 0,
          reference: `YEC-${args.fiscalYear}`,
          transactionType: "Closing Entry",
          fiscalPeriodId: fiscalPeriod._id,
          isPosted: true,
          postedBy: "System",
          postedDate: args.closingDate,
          batchId: batchId
        });

        totalRevenue += revenueAmount;
      }
    }

    // Close Expense Accounts (Credit Expense, Debit Retained Earnings)
    for (const account of expenseAccounts) {
      const balance = accountBalances.get(account._id) || 0;
      const expenseAmount = balance; // Expense accounts have debit balances
      
      if (expenseAmount > 0) {
        // Credit Expense Account
        await ctx.db.insert("journalEntries", {
          entryNumber: `${batchId}-${String(entryCounter++).padStart(3, '0')}`,
          date: args.closingDate,
          accountId: account._id,
          description: `Year-End Closing - ${account.name}`,
          debit: 0,
          credit: expenseAmount,
          reference: `YEC-${args.fiscalYear}`,
          transactionType: "Closing Entry",
          fiscalPeriodId: fiscalPeriod._id,
          isPosted: true,
          postedBy: "System",
          postedDate: args.closingDate,
          batchId: batchId
        });

        totalExpenses += expenseAmount;
      }
    }

    // Net Income to Retained Earnings
    const netIncome = totalRevenue - totalExpenses;
    
    if (netIncome !== 0) {
      await ctx.db.insert("journalEntries", {
        entryNumber: `${batchId}-${String(entryCounter++).padStart(3, '0')}`,
        date: args.closingDate,
        accountId: retainedEarningsAccount._id,
        description: `Year-End Net Income Transfer`,
        debit: netIncome > 0 ? 0 : Math.abs(netIncome),
        credit: netIncome > 0 ? netIncome : 0,
        reference: `YEC-${args.fiscalYear}`,
        transactionType: "Closing Entry",
        fiscalPeriodId: fiscalPeriod._id,
        isPosted: true,
        postedBy: "System",
        postedDate: args.closingDate,
        batchId: batchId
      });
    }

    return {
      success: true,
      message: `✅ Year-end closing entries created successfully\n\n📋 Closing Summary:\n• Fiscal Year: ${args.fiscalYear}\n• Total Revenue: ₱${totalRevenue.toLocaleString()}\n• Total Expenses: ₱${totalExpenses.toLocaleString()}\n• Net Income: ₱${netIncome.toLocaleString()}\n• Batch ID: ${batchId}`,
      fiscalYear: args.fiscalYear,
      totalRevenue,
      totalExpenses,
      netIncome,
      entriesCreated: entryCounter - 1,
      batchId
    };
  }
});
