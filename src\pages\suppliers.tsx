import React, { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import Sidebar from "../components/sidebar";
import Navbar from "../components/navbar";

function Suppliers() {
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingSupplier, setEditingSupplier] = useState<any>(null);
  const [formData, setFormData] = useState<{
    name: string;
    contactEmail: string;
    phone: string;
    address: string;
    supplierType: "Raw Material" | "Components" | "Equipment" | "Services";
    paymentTerms: string;
    qualityRating: number;
  }>({
    name: "",
    contactEmail: "",
    phone: "",
    address: "",
    supplierType: "Raw Material",
    paymentTerms: "Net 30",
    qualityRating: 5
  });

  const suppliers = useQuery(api.tasks.getSuppliers);
  const addSupplier = useMutation(api.tasks.addSupplier);
  const updateSupplier = useMutation(api.tasks.updateSupplier);
  const deleteSupplier = useMutation(api.tasks.deleteSupplier);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      if (editingSupplier) {
        await updateSupplier({
          id: editingSupplier._id,
          ...formData
        });
      } else {
        await addSupplier(formData);
      }
      setShowAddModal(false);
      setEditingSupplier(null);
      setFormData({
        name: "",
        contactEmail: "",
        phone: "",
        address: "",
        supplierType: "Raw Material",
        paymentTerms: "Net 30",
        qualityRating: 5
      });
    } catch (error) {
      alert("Error saving supplier: " + error);
    }
  };

  const handleEdit = (supplier: any) => {
    setEditingSupplier(supplier);
    setFormData({
      name: supplier.name,
      contactEmail: supplier.contactEmail,
      phone: supplier.phone || "",
      address: supplier.address || "",
      supplierType: supplier.supplierType || "Raw Material",
      paymentTerms: supplier.paymentTerms || "Net 30",
      qualityRating: supplier.qualityRating || 5
    });
    setShowAddModal(true);
  };

  const handleDelete = async (supplierId: any) => {
    if (confirm("Are you sure you want to delete this supplier?")) {
      try {
        await deleteSupplier({ id: supplierId });
      } catch (error) {
        alert("Error deleting supplier: " + error);
      }
    }
  };

  if (!suppliers) {
    return (
      <div className="flex h-screen">
        <Sidebar />
        <div className="flex-1 flex flex-col">
          <Navbar />
          <main className="p-6 bg-gray-100 flex-1 overflow-auto">
            <h1 className="text-3xl font-bold mb-6">Suppliers</h1>
            <p className="text-gray-500">Loading...</p>
          </main>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen">
      <Sidebar />
      <div className="flex-1 flex flex-col">
        <Navbar />
        <main className="p-6 bg-gray-100 flex-1 overflow-auto">
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 gap-4">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Suppliers</h1>
              <p className="text-gray-600 mt-1">Manage your supplier relationships and vendor information</p>
            </div>
            <button
              type="button"
              onClick={() => setShowAddModal(true)}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors shadow-sm font-medium flex items-center gap-2"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
              Add Supplier
            </button>
          </div>

          {/* Suppliers Table */}
          <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left py-3 px-4 font-semibold text-gray-700">Supplier Name</th>
                    <th className="text-left py-3 px-4 font-semibold text-gray-700">Contact Email</th>
                    <th className="text-left py-3 px-4 font-semibold text-gray-700">Phone</th>
                    <th className="text-left py-3 px-4 font-semibold text-gray-700">Type</th>
                    <th className="text-left py-3 px-4 font-semibold text-gray-700">Payment Terms</th>
                    <th className="text-center py-3 px-4 font-semibold text-gray-700">Quality Rating</th>
                    <th className="text-center py-3 px-4 font-semibold text-gray-700">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {suppliers.map((supplier) => (
                    <tr key={supplier._id} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-4 px-4 font-medium">{supplier.name}</td>
                      <td className="py-4 px-4">{supplier.contactEmail}</td>
                      <td className="py-4 px-4">{supplier.phone || '-'}</td>
                      <td className="py-4 px-4">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                          {supplier.supplierType}
                        </span>
                      </td>
                      <td className="py-4 px-4">{supplier.paymentTerms}</td>
                      <td className="py-4 px-4 text-center">
                        <div className="flex items-center justify-center">
                          <span className="text-yellow-400">
                            {'★'.repeat(supplier.qualityRating)}
                          </span>
                          <span className="text-gray-300">
                            {'★'.repeat(5 - supplier.qualityRating)}
                          </span>
                          <span className="ml-2 text-sm text-gray-600">
                            ({supplier.qualityRating}/5)
                          </span>
                        </div>
                      </td>
                      <td className="py-4 px-4 text-center">
                        <div className="flex justify-center space-x-2">
                          <button
                            type="button"
                            onClick={() => handleEdit(supplier)}
                            className="inline-flex items-center px-3 py-2 border border-blue-300 text-blue-700 bg-blue-50 rounded-lg hover:bg-blue-100 hover:border-blue-400 transition-all duration-200 text-sm font-medium shadow-sm"
                          >
                            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                            </svg>
                            Edit
                          </button>
                          <button
                            type="button"
                            onClick={() => handleDelete(supplier._id)}
                            className="inline-flex items-center px-3 py-2 border border-red-300 text-red-700 bg-red-50 rounded-lg hover:bg-red-100 hover:border-red-400 transition-all duration-200 text-sm font-medium shadow-sm"
                          >
                            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                            Delete
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Add/Edit Modal */}
          {showAddModal && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
              <div className="bg-white rounded-xl shadow-2xl p-6 w-full max-w-2xl border border-gray-200 max-h-[90vh] overflow-y-auto">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-xl font-bold text-gray-900">
                    {editingSupplier ? 'Edit Supplier' : 'Add New Supplier'}
                  </h3>
                  <button
                    type="button"
                    onClick={() => {
                      setShowAddModal(false);
                      setEditingSupplier(null);
                      setFormData({
                        name: "",
                        contactEmail: "",
                        phone: "",
                        address: "",
                        supplierType: "Raw Material",
                        paymentTerms: "Net 30",
                        qualityRating: 5
                      });
                    }}
                    className="text-gray-400 hover:text-gray-600 transition-colors"
                    aria-label="Close modal"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                <form onSubmit={handleSubmit}>
                  <div className="space-y-6">
                    {/* Basic Information Section */}
                    <div className="bg-gray-50 rounded-lg p-4">
                      <h4 className="text-lg font-semibold text-gray-800 mb-4">Basic Information</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-semibold text-gray-700 mb-2">
                            Supplier Name *
                          </label>
                          <input
                            type="text"
                            value={formData.name}
                            onChange={(e) => setFormData({...formData, name: e.target.value})}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                            placeholder="Enter supplier name"
                            required
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-semibold text-gray-700 mb-2">
                            Contact Email *
                          </label>
                          <input
                            type="email"
                            value={formData.contactEmail}
                            onChange={(e) => setFormData({...formData, contactEmail: e.target.value})}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                            placeholder="<EMAIL>"
                            required
                          />
                        </div>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                        <div>
                          <label className="block text-sm font-semibold text-gray-700 mb-2">
                            Phone Number *
                          </label>
                          <input
                            type="tel"
                            value={formData.phone}
                            onChange={(e) => setFormData({...formData, phone: e.target.value})}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                            placeholder="+****************"
                            required
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-semibold text-gray-700 mb-2">
                            Supplier Type *
                          </label>
                          <select
                            value={formData.supplierType}
                            onChange={(e) => setFormData({...formData, supplierType: e.target.value as any})}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                          >
                            <option value="Raw Material">Raw Material</option>
                            <option value="Components">Components</option>
                            <option value="Equipment">Equipment</option>
                            <option value="Services">Services</option>
                          </select>
                        </div>
                      </div>
                      <div className="mt-4">
                        <label className="block text-sm font-semibold text-gray-700 mb-2">
                          Address *
                        </label>
                        <textarea
                          value={formData.address}
                          onChange={(e) => setFormData({...formData, address: e.target.value})}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                          rows={3}
                          placeholder="Enter complete address"
                          required
                        />
                      </div>
                    </div>

                    {/* Business Terms Section */}
                    <div className="bg-blue-50 rounded-lg p-4">
                      <h4 className="text-lg font-semibold text-gray-800 mb-4">Business Terms</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-semibold text-gray-700 mb-2">
                            Payment Terms
                          </label>
                          <select
                            value={formData.paymentTerms}
                            onChange={(e) => setFormData({...formData, paymentTerms: e.target.value})}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                          >
                            <option value="Net 15">Net 15 Days</option>
                            <option value="Net 30">Net 30 Days</option>
                            <option value="Net 45">Net 45 Days</option>
                            <option value="Net 60">Net 60 Days</option>
                            <option value="Due on Receipt">Due on Receipt</option>
                          </select>
                        </div>
                        <div>
                          <label className="block text-sm font-semibold text-gray-700 mb-2">
                            Quality Rating
                          </label>
                          <select
                            value={formData.qualityRating}
                            onChange={(e) => setFormData({...formData, qualityRating: Number(e.target.value)})}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                          >
                            <option value={1}>⭐ 1 Star - Poor</option>
                            <option value={2}>⭐⭐ 2 Stars - Fair</option>
                            <option value={3}>⭐⭐⭐ 3 Stars - Good</option>
                            <option value={4}>⭐⭐⭐⭐ 4 Stars - Very Good</option>
                            <option value={5}>⭐⭐⭐⭐⭐ 5 Stars - Excellent</option>
                          </select>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-end space-x-3 mt-8 pt-6 border-t border-gray-200">
                    <button
                      type="button"
                      onClick={() => {
                        setShowAddModal(false);
                        setEditingSupplier(null);
                        setFormData({
                          name: "",
                          contactEmail: "",
                          phone: "",
                          address: "",
                          supplierType: "Raw Material",
                          paymentTerms: "Net 30",
                          qualityRating: 5
                        });
                      }}
                      className="px-6 py-3 text-gray-700 bg-gray-100 border border-gray-300 rounded-lg hover:bg-gray-200 transition-all duration-200 font-medium"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all duration-200 font-medium shadow-lg hover:shadow-xl"
                    >
                      {editingSupplier ? 'Update Supplier' : 'Add Supplier'}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          )}
        </main>
      </div>
    </div>
  );
}

export default Suppliers;
