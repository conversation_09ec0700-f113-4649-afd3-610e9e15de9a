import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// PFRS 15 Revenue Recognition Functions

// Function to recognize revenue when performance obligations are satisfied
export const recognizeRevenue = mutation({
  args: {
    invoiceId: v.id("customerInvoices"),
    performanceObligationId: v.id("performanceObligations"),
    recognitionDate: v.string(),
    recognitionAmount: v.number(),
    recognitionMethod: v.union(v.literal("Point in Time"), v.literal("Over Time")),
    percentageComplete: v.optional(v.number())
  },
  handler: async (ctx, args) => {
    // Get the invoice and performance obligation
    const invoice = await ctx.db.get(args.invoiceId);
    const performanceObligation = await ctx.db.get(args.performanceObligationId);
    
    if (!invoice || !performanceObligation) {
      throw new Error("Invoice or Performance Obligation not found");
    }

    // Validate recognition amount doesn't exceed allocated amount
    if (args.recognitionAmount > performanceObligation.allocatedAmount) {
      throw new Error("Recognition amount cannot exceed allocated amount");
    }

    // Get required accounts
    const accounts = await ctx.db.query("accounts").collect();
    const contractLiabilityAccount = accounts.find(acc => acc.code === "202" || acc.name.toLowerCase().includes("contract liability"));
    const revenueAccount = accounts.find(acc => acc.code === "400" || acc.name.toLowerCase().includes("sales revenue"));

    if (!contractLiabilityAccount || !revenueAccount) {
      throw new Error("Required accounts not found: Contract Liability (202) or Sales Revenue (400)");
    }

    // Create journal entries for revenue recognition
    // Debit: Contract Liability (decrease liability)
    await ctx.db.insert("journalEntries", {
      accountId: contractLiabilityAccount._id,
      date: args.recognitionDate,
      description: `Revenue recognition for Invoice ${invoice.invoiceNumber} - Performance Obligation: ${performanceObligation.description}`,
      debit: args.recognitionAmount,
      credit: 0,
      reference: `REV-${invoice.invoiceNumber}`,
      transactionType: "Sales",
      departmentId: "",
      costCenterId: ""
    });

    // Credit: Sales Revenue (increase revenue)
    await ctx.db.insert("journalEntries", {
      accountId: revenueAccount._id,
      date: args.recognitionDate,
      description: `Revenue recognition for Invoice ${invoice.invoiceNumber} - Performance Obligation: ${performanceObligation.description}`,
      debit: 0,
      credit: args.recognitionAmount,
      reference: `REV-${invoice.invoiceNumber}`,
      transactionType: "Sales",
      departmentId: "",
      costCenterId: ""
    });

    // Update performance obligation status
    const newStatus = args.recognitionAmount >= performanceObligation.allocatedAmount ? "Satisfied" : "In Progress";
    await ctx.db.patch(args.performanceObligationId, {
      status: newStatus,
      satisfactionDate: args.recognitionDate,
      satisfactionMethod: args.recognitionMethod,
      percentageComplete: args.percentageComplete || 100
    });

    // Update invoice revenue recognition status
    const currentRecognized = invoice.revenueRecognizedAmount || 0;
    const newRecognizedAmount = currentRecognized + args.recognitionAmount;
    const newRevenueStatus = newRecognizedAmount >= invoice.totalAmount ? "Fully Recognized" : "Partially Recognized";

    await ctx.db.patch(args.invoiceId, {
      revenueRecognitionStatus: newRevenueStatus,
      revenueRecognizedAmount: newRecognizedAmount,
      controlTransferredDate: args.recognitionDate
    });

    return {
      success: true,
      message: `Revenue of ₱${args.recognitionAmount.toLocaleString()} recognized for Invoice ${invoice.invoiceNumber}`,
      revenueRecognized: args.recognitionAmount,
      totalRecognized: newRecognizedAmount,
      remainingToRecognize: invoice.totalAmount - newRecognizedAmount
    };
  }
});

// Function to create performance obligations for an invoice
export const createPerformanceObligations = mutation({
  args: {
    invoiceId: v.id("customerInvoices"),
    obligations: v.array(v.object({
      description: v.string(),
      allocatedAmount: v.number(),
      satisfactionMethod: v.union(v.literal("Point in Time"), v.literal("Over Time"))
    }))
  },
  handler: async (ctx, args) => {
    const invoice = await ctx.db.get(args.invoiceId);
    if (!invoice) {
      throw new Error("Invoice not found");
    }

    // Validate total allocated amount equals invoice total
    const totalAllocated = args.obligations.reduce((sum, obligation) => sum + obligation.allocatedAmount, 0);
    if (Math.abs(totalAllocated - invoice.totalAmount) > 0.01) {
      throw new Error(`Total allocated amount (₱${totalAllocated}) must equal invoice total (₱${invoice.totalAmount})`);
    }

    // Create performance obligations
    const obligationIds = [];
    for (const obligation of args.obligations) {
      const obligationId = await ctx.db.insert("performanceObligations", {
        invoiceId: args.invoiceId,
        description: obligation.description,
        allocatedAmount: obligation.allocatedAmount,
        status: "Pending",
        satisfactionMethod: obligation.satisfactionMethod
      });
      obligationIds.push(obligationId);
    }

    // Update invoice status
    await ctx.db.patch(args.invoiceId, {
      revenueRecognitionStatus: "Not Recognized",
      revenueRecognizedAmount: 0,
      performanceObligationsFulfilled: false
    });

    return {
      success: true,
      message: `Created ${args.obligations.length} performance obligations for Invoice ${invoice.invoiceNumber}`,
      obligationIds,
      totalAllocated
    };
  }
});

// Function to get revenue recognition status
export const getRevenueRecognitionStatus = query(async (ctx) => {
  const invoices = await ctx.db.query("customerInvoices").collect();
  const performanceObligations = await ctx.db.query("performanceObligations").collect();
  const customers = await ctx.db.query("customers").collect();

  const revenueData = invoices.map(invoice => {
    const customer = customers.find(c => c._id === invoice.customerId);
    const obligations = performanceObligations.filter(po => po.invoiceId === invoice._id);
    
    const totalAllocated = obligations.reduce((sum, po) => sum + po.allocatedAmount, 0);
    const satisfiedObligations = obligations.filter(po => po.status === "Satisfied");
    const recognizedAmount = invoice.revenueRecognizedAmount || 0;
    const remainingToRecognize = invoice.totalAmount - recognizedAmount;

    return {
      ...invoice,
      customerName: customer?.name || "Unknown",
      totalPerformanceObligations: obligations.length,
      satisfiedObligations: satisfiedObligations.length,
      totalAllocated,
      recognizedAmount,
      remainingToRecognize,
      recognitionPercentage: invoice.totalAmount > 0 ? (recognizedAmount / invoice.totalAmount) * 100 : 0,
      obligations: obligations.map(po => ({
        ...po,
        isOverdue: po.status === "Pending" && new Date(invoice.dueDate) < new Date()
      }))
    };
  });

  const summary = {
    totalInvoices: invoices.length,
    fullyRecognized: invoices.filter(inv => inv.revenueRecognitionStatus === "Fully Recognized").length,
    partiallyRecognized: invoices.filter(inv => inv.revenueRecognitionStatus === "Partially Recognized").length,
    notRecognized: invoices.filter(inv => inv.revenueRecognitionStatus === "Not Recognized" || !inv.revenueRecognitionStatus).length,
    totalContractValue: invoices.reduce((sum, inv) => sum + inv.totalAmount, 0),
    totalRecognizedRevenue: invoices.reduce((sum, inv) => sum + (inv.revenueRecognizedAmount || 0), 0),
    totalDeferredRevenue: invoices.reduce((sum, inv) => sum + (inv.totalAmount - (inv.revenueRecognizedAmount || 0)), 0)
  };

  return {
    invoices: revenueData,
    summary
  };
});

// Function to calculate and post monthly depreciation (PAS 16)
export const calculateMonthlyDepreciation = mutation({
  args: {
    periodDate: v.string() // YYYY-MM format
  },
  handler: async (ctx, args) => {
    const fixedAssets = await ctx.db.query("fixedAssets")
      .filter(q => q.eq(q.field("status"), "Active"))
      .collect();

    const accounts = await ctx.db.query("accounts").collect();
    const depreciationExpenseAccount = accounts.find(acc => acc.code === "506" || acc.name.toLowerCase().includes("depreciation expense"));
    
    if (!depreciationExpenseAccount) {
      throw new Error("Depreciation Expense account (506) not found");
    }

    let totalDepreciation = 0;
    const depreciationEntries = [];

    for (const asset of fixedAssets) {
      // Calculate monthly depreciation
      const monthlyDepreciation = asset.monthlyDepreciation || 
        ((asset.purchaseCost - asset.salvageValue) / (asset.usefulLife * 12));

      // Check if depreciation should continue
      const maxDepreciation = asset.purchaseCost - asset.salvageValue;
      if (asset.accumulatedDepreciation >= maxDepreciation) {
        continue; // Asset fully depreciated
      }

      // Ensure we don't over-depreciate
      const remainingDepreciation = maxDepreciation - asset.accumulatedDepreciation;
      const actualDepreciation = Math.min(monthlyDepreciation, remainingDepreciation);

      if (actualDepreciation <= 0) continue;

      // Find or create accumulated depreciation account for this asset
      let accumulatedDepreciationAccount = accounts.find(acc => 
        acc._id === asset.accumulatedDepreciationAccountId ||
        (acc.code === "119" || acc.name.toLowerCase().includes("accumulated depreciation"))
      );

      if (!accumulatedDepreciationAccount) {
        throw new Error(`Accumulated Depreciation account not found for asset ${asset.assetNumber}`);
      }

      // Create depreciation schedule entry
      const scheduleId = await ctx.db.insert("depreciationSchedule", {
        assetId: asset._id,
        periodDate: args.periodDate,
        openingBookValue: asset.currentBookValue,
        depreciationExpense: actualDepreciation,
        accumulatedDepreciation: asset.accumulatedDepreciation + actualDepreciation,
        closingBookValue: asset.currentBookValue - actualDepreciation,
        isPosted: false
      });

      // Create journal entries
      // Debit: Depreciation Expense
      const debitEntryId = await ctx.db.insert("journalEntries", {
        accountId: depreciationExpenseAccount._id,
        date: `${args.periodDate}-01`,
        description: `Monthly depreciation for ${asset.assetName} (${asset.assetNumber})`,
        debit: actualDepreciation,
        credit: 0,
        reference: `DEP-${args.periodDate}-${asset.assetNumber}`,
        transactionType: "Depreciation",
        departmentId: "",
        costCenterId: ""
      });

      // Credit: Accumulated Depreciation
      await ctx.db.insert("journalEntries", {
        accountId: accumulatedDepreciationAccount._id,
        date: `${args.periodDate}-01`,
        description: `Monthly depreciation for ${asset.assetName} (${asset.assetNumber})`,
        debit: 0,
        credit: actualDepreciation,
        reference: `DEP-${args.periodDate}-${asset.assetNumber}`,
        transactionType: "Depreciation",
        departmentId: "",
        costCenterId: ""
      });

      // Update asset values
      await ctx.db.patch(asset._id, {
        accumulatedDepreciation: asset.accumulatedDepreciation + actualDepreciation,
        currentBookValue: asset.currentBookValue - actualDepreciation,
        lastDepreciationDate: `${args.periodDate}-01`,
        monthlyDepreciation: actualDepreciation
      });

      // Mark schedule as posted
      await ctx.db.patch(scheduleId, {
        isPosted: true,
        journalEntryId: debitEntryId
      });

      totalDepreciation += actualDepreciation;
      depreciationEntries.push({
        assetNumber: asset.assetNumber,
        assetName: asset.assetName,
        depreciation: actualDepreciation,
        newBookValue: asset.currentBookValue - actualDepreciation
      });
    }

    return {
      success: true,
      message: `Depreciation calculated and posted for ${depreciationEntries.length} assets`,
      totalDepreciation,
      period: args.periodDate,
      assetsDepreciated: depreciationEntries.length,
      depreciationEntries
    };
  }
});

// PFRS/PAS 2 Inventory Costing Functions

// Function to record inventory purchase with proper costing
export const recordInventoryPurchase = mutation({
  args: {
    inventoryId: v.id("inventory"),
    quantity: v.number(),
    unitCost: v.number(),
    purchaseDate: v.string(),
    referenceNumber: v.string(),
    supplierId: v.optional(v.id("suppliers"))
  },
  handler: async (ctx, args) => {
    const inventory = await ctx.db.get(args.inventoryId);
    if (!inventory) {
      throw new Error("Inventory item not found");
    }

    const totalCost = args.quantity * args.unitCost;

    // Create cost layer for FIFO tracking
    await ctx.db.insert("inventoryCostLayers", {
      inventoryId: args.inventoryId,
      transactionType: "Purchase",
      transactionDate: args.purchaseDate,
      quantity: args.quantity,
      unitCost: args.unitCost,
      totalCost: totalCost,
      remainingQuantity: args.quantity,
      remainingCost: totalCost,
      referenceNumber: args.referenceNumber,
      isActive: true
    });

    // Update inventory quantities and costs
    const newQuantity = inventory.currentStock + args.quantity;
    let newUnitCost = args.unitCost;

    if (inventory.costingMethod === "Weighted Average") {
      // Calculate weighted average cost
      const currentValue = inventory.currentStock * inventory.unitCost;
      const newValue = args.quantity * args.unitCost;
      newUnitCost = newQuantity > 0 ? (currentValue + newValue) / newQuantity : args.unitCost;
    } else if (inventory.costingMethod === "FIFO") {
      // For FIFO, keep the unit cost as the most recent purchase cost for display
      // Actual costing will be done during sales using cost layers
      newUnitCost = args.unitCost;
    }

    await ctx.db.patch(args.inventoryId, {
      currentStock: newQuantity,
      unitCost: newUnitCost,
      averageCost: newUnitCost,
      lastCostUpdate: args.purchaseDate
    });

    // Record inventory transaction
    await ctx.db.insert("inventoryTransactions", {
      inventoryId: args.inventoryId,
      transactionType: "Purchase Receipt",
      transactionDate: args.purchaseDate,
      quantityIn: args.quantity,
      quantityOut: 0,
      unitCost: args.unitCost,
      totalCost: totalCost,
      runningBalance: newQuantity,
      referenceNumber: args.referenceNumber,
      description: `Purchase from ${args.supplierId ? "supplier" : "vendor"} - ${args.referenceNumber}`
    });

    return {
      success: true,
      message: `Inventory purchase recorded: ${args.quantity} units at ₱${args.unitCost} each`,
      newQuantity,
      newUnitCost,
      totalCost
    };
  }
});

// Function to issue inventory with proper COGS calculation
export const issueInventory = mutation({
  args: {
    inventoryId: v.id("inventory"),
    quantity: v.number(),
    issueDate: v.string(),
    referenceNumber: v.string(),
    issueType: v.union(v.literal("Sale"), v.literal("Production"), v.literal("Adjustment"))
  },
  handler: async (ctx, args) => {
    const inventory = await ctx.db.get(args.inventoryId);
    if (!inventory) {
      throw new Error("Inventory item not found");
    }

    if (inventory.currentStock < args.quantity) {
      throw new Error(`Insufficient stock. Available: ${inventory.currentStock}, Requested: ${args.quantity}`);
    }

    let totalCOGS = 0;
    let remainingToIssue = args.quantity;

    if (inventory.costingMethod === "FIFO") {
      // Use FIFO method - consume oldest cost layers first
      const costLayers = await ctx.db.query("inventoryCostLayers")
        .filter(q => q.and(
          q.eq(q.field("inventoryId"), args.inventoryId),
          q.eq(q.field("isActive"), true),
          q.gt(q.field("remainingQuantity"), 0)
        ))
        .order("asc")
        .collect();

      for (const layer of costLayers) {
        if (remainingToIssue <= 0) break;

        const quantityFromLayer = Math.min(remainingToIssue, layer.remainingQuantity);
        const costFromLayer = quantityFromLayer * layer.unitCost;

        totalCOGS += costFromLayer;
        remainingToIssue -= quantityFromLayer;

        // Update cost layer
        const newRemainingQuantity = layer.remainingQuantity - quantityFromLayer;
        const newRemainingCost = layer.remainingCost - costFromLayer;

        await ctx.db.patch(layer._id, {
          remainingQuantity: newRemainingQuantity,
          remainingCost: newRemainingCost,
          isActive: newRemainingQuantity > 0
        });
      }
    } else if (inventory.costingMethod === "Weighted Average") {
      // Use weighted average cost
      const averageCost = inventory.averageCost ?? inventory.unitCost;
      totalCOGS = args.quantity * averageCost;
    }

    if (remainingToIssue > 0) {
      throw new Error(`Unable to issue full quantity. Missing cost layers for ${remainingToIssue} units.`);
    }

    // Update inventory quantity
    const newQuantity = inventory.currentStock - args.quantity;
    await ctx.db.patch(args.inventoryId, {
      currentStock: newQuantity
    });

    // Record inventory transaction
    await ctx.db.insert("inventoryTransactions", {
      inventoryId: args.inventoryId,
      transactionType: args.issueType === "Sale" ? "Sales Issue" : "Material Issue",
      transactionDate: args.issueDate,
      quantityIn: 0,
      quantityOut: args.quantity,
      unitCost: totalCOGS / args.quantity,
      totalCost: totalCOGS,
      runningBalance: newQuantity,
      referenceNumber: args.referenceNumber,
      description: `${args.issueType} - ${args.referenceNumber}`
    });

    // Create COGS journal entry if this is a sale
    if (args.issueType === "Sale") {
      const accounts = await ctx.db.query("accounts").collect();
      const cogsAccount = accounts.find(acc => acc.code === "500" || acc.name.toLowerCase().includes("cost of goods sold"));
      const inventoryAccount = accounts.find(acc => acc.code === "115" || acc.name.toLowerCase().includes("inventory"));

      if (cogsAccount && inventoryAccount) {
        // Debit: Cost of Goods Sold
        await ctx.db.insert("journalEntries", {
          accountId: cogsAccount._id,
          date: args.issueDate,
          description: `COGS for ${inventory.partName} - ${args.referenceNumber}`,
          debit: totalCOGS,
          credit: 0,
          reference: args.referenceNumber,
          transactionType: "Sales",
          departmentId: "",
          costCenterId: ""
        });

        // Credit: Inventory
        await ctx.db.insert("journalEntries", {
          accountId: inventoryAccount._id,
          date: args.issueDate,
          description: `Inventory reduction for ${inventory.partName} - ${args.referenceNumber}`,
          debit: 0,
          credit: totalCOGS,
          reference: args.referenceNumber,
          transactionType: "Sales",
          departmentId: "",
          costCenterId: ""
        });
      }
    }

    return {
      success: true,
      message: `Inventory issued: ${args.quantity} units with COGS of ₱${totalCOGS.toLocaleString()}`,
      quantityIssued: args.quantity,
      totalCOGS,
      averageCOGS: totalCOGS / args.quantity,
      newQuantity,
      costingMethod: inventory.costingMethod
    };
  }
});

// Philippine VAT and Tax Compliance Functions

// Function to calculate and record VAT for sales transactions
export const calculateSalesVAT = mutation({
  args: {
    invoiceId: v.id("customerInvoices"),
    vatableAmount: v.number(),
    vatExemptAmount: v.optional(v.number()),
    zeroRatedAmount: v.optional(v.number()),
    vatRate: v.optional(v.number()) // Default 12% for Philippines
  },
  handler: async (ctx, args) => {
    const invoice = await ctx.db.get(args.invoiceId);
    if (!invoice) {
      throw new Error("Invoice not found");
    }

    const vatRate = args.vatRate || 0.12; // 12% VAT rate for Philippines
    const vatAmount = args.vatableAmount * vatRate;
    const vatExemptAmount = args.vatExemptAmount || 0;
    const zeroRatedAmount = args.zeroRatedAmount || 0;

    // Validate amounts
    const totalCalculated = args.vatableAmount + vatExemptAmount + zeroRatedAmount;
    const netAmount = invoice.totalAmount - invoice.taxAmount;

    if (Math.abs(totalCalculated - netAmount) > 0.01) {
      throw new Error(`VAT breakdown total (₱${totalCalculated}) must equal net invoice amount (₱${netAmount})`);
    }

    // Get VAT accounts
    const accounts = await ctx.db.query("accounts").collect();
    const outputVatAccount = accounts.find(acc => acc.code === "201" || acc.name.toLowerCase().includes("output tax"));

    if (!outputVatAccount) {
      throw new Error("Output VAT account (201) not found");
    }

    // Record VAT transaction
    await ctx.db.insert("vatTransactions", {
      transactionDate: invoice.invoiceDate,
      transactionType: "Sales",
      documentType: "Sales Invoice",
      documentNumber: invoice.invoiceNumber,
      customerId: invoice.customerId,
      vatableAmount: args.vatableAmount,
      vatExemptAmount: vatExemptAmount,
      zeroRatedAmount: zeroRatedAmount,
      vatAmount: vatAmount,
      vatRate: vatRate,
      withholdingTaxAmount: 0,
      withholdingTaxRate: 0,
      netAmount: args.vatableAmount + vatExemptAmount + zeroRatedAmount,
      totalAmount: invoice.totalAmount
    });

    // Update invoice with VAT breakdown
    await ctx.db.patch(args.invoiceId, {
      vatableAmount: args.vatableAmount,
      vatExemptAmount: vatExemptAmount,
      zeroRatedAmount: zeroRatedAmount,
      outputVatAmount: vatAmount,
      taxAmount: vatAmount
    });

    return {
      success: true,
      message: `VAT calculated for Invoice ${invoice.invoiceNumber}`,
      vatableAmount: args.vatableAmount,
      vatAmount: vatAmount,
      vatRate: vatRate * 100,
      vatExemptAmount,
      zeroRatedAmount,
      totalAmount: invoice.totalAmount
    };
  }
});

// Function to calculate and record VAT for purchase transactions
export const calculatePurchaseVAT = mutation({
  args: {
    vendorInvoiceId: v.id("vendorInvoices"),
    vatableAmount: v.number(),
    vatExemptAmount: v.optional(v.number()),
    zeroRatedAmount: v.optional(v.number()),
    vatRate: v.optional(v.number()),
    withholdingTaxRate: v.optional(v.number())
  },
  handler: async (ctx, args) => {
    const vendorInvoice = await ctx.db.get(args.vendorInvoiceId);
    if (!vendorInvoice) {
      throw new Error("Vendor invoice not found");
    }

    const vatRate = args.vatRate || 0.12; // 12% VAT rate
    const withholdingTaxRate = args.withholdingTaxRate || 0; // Varies by transaction type

    const inputVatAmount = args.vatableAmount * vatRate;
    const withholdingTaxAmount = args.vatableAmount * withholdingTaxRate;
    const vatExemptAmount = args.vatExemptAmount || 0;
    const zeroRatedAmount = args.zeroRatedAmount || 0;

    // Get required accounts
    const accounts = await ctx.db.query("accounts").collect();
    const inputVatAccount = accounts.find(acc => acc.code === "118" || acc.name.toLowerCase().includes("input tax"));
    const withholdingTaxAccount = accounts.find(acc => acc.code === "119" || acc.name.toLowerCase().includes("withholding tax"));

    if (!inputVatAccount) {
      throw new Error("Input VAT account (118) not found");
    }

    // Record VAT transaction
    await ctx.db.insert("vatTransactions", {
      transactionDate: vendorInvoice.invoiceDate,
      transactionType: "Purchase",
      documentType: "Purchase Invoice",
      documentNumber: vendorInvoice.invoiceNumber,
      supplierId: vendorInvoice.supplierId,
      vatableAmount: args.vatableAmount,
      vatExemptAmount: vatExemptAmount,
      zeroRatedAmount: zeroRatedAmount,
      vatAmount: inputVatAmount,
      vatRate: vatRate,
      withholdingTaxAmount: withholdingTaxAmount,
      withholdingTaxRate: withholdingTaxRate,
      netAmount: args.vatableAmount + vatExemptAmount + zeroRatedAmount,
      totalAmount: vendorInvoice.totalAmount
    });

    // Create journal entries for Input VAT
    if (inputVatAmount > 0) {
      await ctx.db.insert("journalEntries", {
        accountId: inputVatAccount._id,
        date: vendorInvoice.invoiceDate,
        description: `Input VAT on Invoice ${vendorInvoice.invoiceNumber}`,
        debit: inputVatAmount,
        credit: 0,
        reference: vendorInvoice.invoiceNumber,
        transactionType: "Purchase",
        departmentId: "",
        costCenterId: ""
      });
    }

    // Create journal entries for Withholding Tax
    if (withholdingTaxAmount > 0 && withholdingTaxAccount) {
      await ctx.db.insert("journalEntries", {
        accountId: withholdingTaxAccount._id,
        date: vendorInvoice.invoiceDate,
        description: `Withholding Tax on Invoice ${vendorInvoice.invoiceNumber}`,
        debit: withholdingTaxAmount,
        credit: 0,
        reference: vendorInvoice.invoiceNumber,
        transactionType: "Purchase",
        departmentId: "",
        costCenterId: ""
      });
    }

    return {
      success: true,
      message: `VAT calculated for Purchase Invoice ${vendorInvoice.invoiceNumber}`,
      vatableAmount: args.vatableAmount,
      inputVatAmount: inputVatAmount,
      withholdingTaxAmount: withholdingTaxAmount,
      vatRate: vatRate * 100,
      withholdingTaxRate: withholdingTaxRate * 100,
      netPayable: vendorInvoice.totalAmount - withholdingTaxAmount
    };
  }
});

// Function to generate VAT summary report for BIR compliance
export const getVATSummaryReport = query({
  args: {
    startDate: v.string(),
    endDate: v.string()
  },
  handler: async (ctx, args) => {
    const vatTransactions = await ctx.db.query("vatTransactions")
      .filter(q => q.and(
        q.gte(q.field("transactionDate"), args.startDate),
        q.lte(q.field("transactionDate"), args.endDate)
      ))
      .collect();

    const salesTransactions = vatTransactions.filter(vt => vt.transactionType === "Sales");
    const purchaseTransactions = vatTransactions.filter(vt => vt.transactionType === "Purchase");

    // Sales VAT Summary
    const salesSummary = {
      totalVatableSales: salesTransactions.reduce((sum, vt) => sum + vt.vatableAmount, 0),
      totalVatExemptSales: salesTransactions.reduce((sum, vt) => sum + vt.vatExemptAmount, 0),
      totalZeroRatedSales: salesTransactions.reduce((sum, vt) => sum + vt.zeroRatedAmount, 0),
      totalOutputVAT: salesTransactions.reduce((sum, vt) => sum + vt.vatAmount, 0),
      totalSales: salesTransactions.reduce((sum, vt) => sum + vt.totalAmount, 0)
    };

    // Purchase VAT Summary
    const purchaseSummary = {
      totalVatablePurchases: purchaseTransactions.reduce((sum, vt) => sum + vt.vatableAmount, 0),
      totalVatExemptPurchases: purchaseTransactions.reduce((sum, vt) => sum + vt.vatExemptAmount, 0),
      totalZeroRatedPurchases: purchaseTransactions.reduce((sum, vt) => sum + vt.zeroRatedAmount, 0),
      totalInputVAT: purchaseTransactions.reduce((sum, vt) => sum + vt.vatAmount, 0),
      totalWithholdingTax: purchaseTransactions.reduce((sum, vt) => sum + vt.withholdingTaxAmount, 0),
      totalPurchases: purchaseTransactions.reduce((sum, vt) => sum + vt.totalAmount, 0)
    };

    // VAT Payable Calculation
    const vatPayable = Math.max(0, salesSummary.totalOutputVAT - purchaseSummary.totalInputVAT);
    const vatRefundable = Math.max(0, purchaseSummary.totalInputVAT - salesSummary.totalOutputVAT);

    return {
      period: {
        startDate: args.startDate,
        endDate: args.endDate
      },
      salesSummary,
      purchaseSummary,
      vatPayable,
      vatRefundable,
      netVATPosition: salesSummary.totalOutputVAT - purchaseSummary.totalInputVAT,
      totalTransactions: vatTransactions.length,
      salesTransactionCount: salesTransactions.length,
      purchaseTransactionCount: purchaseTransactions.length
    };
  }
});
