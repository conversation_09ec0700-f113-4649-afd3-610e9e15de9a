import Sidebar from "../components/sidebar";
import Navbar from "../components/navbar";

function Notifications() {
  return (
    <div className="flex h-screen">
      <Sidebar />
      <div className="flex-1 flex flex-col">
        <Navbar />
        <main className="p-6 bg-gray-100 flex-1 overflow-auto">
          <h1 className="text-3xl font-bold mb-6">Notifications</h1>
          <div className="bg-white p-8 rounded-lg shadow-md">
            <p className="text-gray-600">
              This page will manage system notifications and alerts.
            </p>
            <div className="mt-4 text-sm text-gray-500">
              Features to be implemented:
              <ul className="list-disc list-inside mt-2">
                <li>View all notifications</li>
                <li>Mark notifications as read</li>
                <li>Configure notification preferences</li>
                <li>Set up automated alerts</li>
                <li>Email notification settings</li>
                <li>Push notification settings</li>
              </ul>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}

export default Notifications;
