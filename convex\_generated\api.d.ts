/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as assetManagement from "../assetManagement.js";
import type * as balanceControl from "../balanceControl.js";
import type * as birCompliance from "../birCompliance.js";
import type * as budgetManagement from "../budgetManagement.js";
import type * as expenseManagement from "../expenseManagement.js";
import type * as finance_getDashboardData from "../finance/getDashboardData.js";
import type * as finance_seedData from "../finance/seedData.js";
import type * as financialManagement from "../financialManagement.js";
import type * as financialReporting from "../financialReporting.js";
import type * as fiscalPeriodManagement from "../fiscalPeriodManagement.js";
import type * as philippineGAAP from "../philippineGAAP.js";
import type * as tasks from "../tasks.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  assetManagement: typeof assetManagement;
  balanceControl: typeof balanceControl;
  birCompliance: typeof birCompliance;
  budgetManagement: typeof budgetManagement;
  expenseManagement: typeof expenseManagement;
  "finance/getDashboardData": typeof finance_getDashboardData;
  "finance/seedData": typeof finance_seedData;
  financialManagement: typeof financialManagement;
  financialReporting: typeof financialReporting;
  fiscalPeriodManagement: typeof fiscalPeriodManagement;
  philippineGAAP: typeof philippineGAAP;
  tasks: typeof tasks;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;
